<?php
/**
 * Realome functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Realome
 * @since Realome 1.0
 */

if ( ! function_exists( 'realome_support' ) ) :

	/**
	 * Sets up theme defaults and registers support for various WordPress features.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_support() {

		// Add support for block styles.
		add_theme_support( 'wp-block-styles' );

		// Add support for WooCommerce.
		add_theme_support( 'woocommerce' );

	// Add Customizer support for block themes
	add_theme_support( 'customize-selective-refresh-widgets' );
	add_theme_support( 'custom-logo' );
	add_theme_support( 'custom-header' );
	add_theme_support( 'custom-background' );

		// Enqueue editor styles.
		add_editor_style(
			array(
				'style.css',
				'/build/style-index.css',
			)
		);

		add_image_size( 'square-medium', 1000, 1000, true );
		add_image_size( 'square-small', 500, 500, true );

		add_image_size( 'landscape', 1300, 975, true );
		add_image_size( 'portrait', 1300, 1736, true );

		add_image_size( 'landscape-medium', 1000, 750, true );
		add_image_size( 'portrait-medium', 1000, 1335, true );

		add_image_size( 'landscape-small', 632, 474, true );
		add_image_size( 'portrait-small', 632, 844, true );

	}

endif;

add_action( 'after_setup_theme', 'realome_support' );

/**
 * Make custom sizes selectable from WordPress admin.
 *
 * @param array $sizes images sizes.
 */
function realome_custom_image_sizes( $sizes ) {
	return array_merge(
		$sizes,
		array(
			'square-medium'    => __( 'Square Medium', 'realome' ),
			'square-small'     => __( 'Square Small', 'realome' ),
			'landscape'        => __( 'Landscape', 'realome' ),
			'landscape-medium' => __( 'Landscape Medium', 'realome' ),
			'landscape-small'  => __( 'Landscape Small', 'realome' ),
			'portrait'         => __( 'Portrait', 'realome' ),
			'portrait-medium'  => __( 'Portrait Medium', 'realome' ),
			'portrait-small'   => __( 'Portrait Small', 'realome' ),
		)
	);
}
add_filter( 'image_size_names_choose', 'realome_custom_image_sizes' );

if ( ! function_exists( 'realome_scripts' ) ) :

	/**
	 * Enqueue scripts and styles.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_scripts() {

		// Register theme stylesheet.
		$theme_version  = wp_get_theme()->get( 'Version' );
		$version_string = is_string( $theme_version ) ? $theme_version : false;
		wp_register_style(
			'realome-style',
			get_template_directory_uri() . '/style.css',
			array(),
			$version_string
		);

		// Add styles inline.
		wp_add_inline_style( 'realome-style', realome_get_font_face_styles() );

		// Add animation styles inline.
		wp_add_inline_style( 'realome-style', realome_get_animation_styles() );

		// Add cursor tracking styles inline.
		wp_add_inline_style( 'realome-style', realome_get_cursor_tracking_styles() );

		// Add Gravity Forms styles inline.
		wp_add_inline_style( 'realome-style', realome_get_gform_styles() );

		// Enqueue theme stylesheet.
		wp_enqueue_style( 'realome-style' );

		// Register theme stylesheet.
		wp_register_style(
			'realome-theme-style',
			get_template_directory_uri() . '/build/style-index.css',
			array(),
			$version_string
		);

		// Enqueue theme stylesheet.
		wp_enqueue_style( 'realome-theme-style' );

		// Register and enqueue custom styles.
		wp_register_style(
			'realome-custom-styles',
			get_template_directory_uri() . '/assets/css/custom-styles.css',
			array('realome-theme-style'),
			$version_string
		);
		wp_enqueue_style( 'realome-custom-styles' );

		// Register material icons stylesheet.
		wp_register_style(
			'material-icons',
			'https://fonts.googleapis.com/icon?family=Material+Icons+Outlined',
			array(),
			'1.0.0'
		);
		wp_enqueue_style( 'material-icons' );

		// Responsive embeds script.
		wp_enqueue_script(
			'realome-responsive-embeds-script',
			get_template_directory_uri() . '/assets/js/responsive-embeds.js',
			array(),
			wp_get_theme()->get( 'Version' ),
			$version_string
		);

		// GSAP Core Library
		wp_enqueue_script(
			'gsap-core',
			'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js',
			array(),
			'3.12.2',
			true
		);

		// GSAP DrawSVG Plugin (trial version for text effects)
		wp_enqueue_script(
			'gsap-drawsvg',
			'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/DrawSVGPlugin.min.js',
			array('gsap-core'),
			'3.12.2',
			true
		);

		// Gsap main file from local
		wp_enqueue_script(
			'gsap-local',
			get_template_directory_uri() . '/assets/js/gsap.min.js',
			array('gsap-core'),
			$version_string,
			true
		);
				// Gsap main file from local
		wp_enqueue_script(
			'gsap-scroll-trigger',
			get_template_directory_uri() . '/assets/js/ScrollTrigger.min.js',
			array('gsap-core'),
			$version_string,
			true
		);

					// Gsap main file from local
		wp_enqueue_script(
			'gsap-split-text',
			get_template_directory_uri() . '/assets/js/SplitText.min.js',
			array('gsap-core'),
			$version_string,
			true
		);
		
		// Theme animations script (buttons only)
		wp_enqueue_script(
			'realome-animations',
			get_template_directory_uri() . '/assets/js/animations.js',
			array('gsap-core'),
			$version_string,
			true
		);

		// Text drawing animation script
		wp_enqueue_script(
			'realome-text-drawing',
			get_template_directory_uri() . '/assets/js/text-drawing.js',
			array('gsap-core', 'gsap-drawsvg'),
			$version_string,
			true
		);

		// Cursor tracking script for homepage
		wp_enqueue_script(
			'realome-cursor-tracking',
			get_template_directory_uri() . '/assets/js/cursor-tracking.js',
			array('gsap-core'),
			$version_string,
			true
		);
	}

endif;

add_action( 'wp_enqueue_scripts', 'realome_scripts' );

if ( ! function_exists( 'realome_editor_styles' ) ) :

	/**
	 * Enqueue editor styles.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_inline_editor_styles() {

		// Add styles inline.
		wp_add_inline_style( 'wp-block-library', realome_get_font_face_styles() );
		wp_add_inline_style( 'wp-block-library', realome_editor_styles() );
	}

endif;

add_action( 'admin_init', 'realome_inline_editor_styles' );


if ( ! function_exists( 'realome_get_font_face_styles' ) ) :

	/**
	 * Get font face styles.
	 * Called by functions realome_scripts() and realome_editor_styles() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_font_face_styles() {

		return "
		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/inter-roman.woff2' ) . "') format('woff2');
			font-style: 400;
			font-weight: normal;
			font-display: swap;

		}

		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/Inter-medium.woff2' ) . "') format('woff2');
			font-style: normal;
			font-weight: 500;
			font-display: swap;
		}

		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/inter-semibold.woff2' ) . "') format('woff2');
			font-style: normal;
			font-weight: 600;
			font-display: swap;
		}

		@font-face{
			font-family: 'Inter';
			src: url('" . get_theme_file_uri( 'assets/fonts/inter-italic.woff2' ) . "') format('woff2');
			font-style: italic;
			font-weight: normal;
			font-display: swap;
		}

		/* fallback */
		@font-face {
		  font-family: 'Material Icons Outlined';
		  font-style: normal;
		  font-weight: 400;
		  src: url(https://fonts.gstatic.com/s/materialiconsoutlined/v105/gok-H7zzDkdnRel8-DQ6KAXJ69wP1tGnf4ZGhUce.woff2) format('woff2');
		}
		
		.material-icons-outlined {
		  font-family: 'Material Icons Outlined';
		  font-weight: normal;
		  font-style: normal;
		  font-size: 24px;
		  line-height: 1;
		  letter-spacing: normal;
		  text-transform: none;
		  display: inline-block;
		  white-space: nowrap;
		  word-wrap: normal;
		  direction: ltr;
		  -webkit-font-feature-settings: 'liga';
		  -webkit-font-smoothing: antialiased;
		}

		";

	}

endif;

if ( ! function_exists( 'realome_preload_webfonts' ) ) :

	/**
	 * Preloads the main web font to improve performance.
	 *
	 * Only the main web font (font-style: normal) is preloaded here since that font is always relevant (it is used
	 * on every heading, for example). The other font is only needed if there is any applicable content in italic style,
	 * and therefore preloading it would in most cases regress performance when that font would otherwise not be loaded
	 * at all.
	 *
	 * @since Realome 1.0
	 *
	 * @return void
	 */
	function realome_preload_webfonts() {
		?>
		<link rel="preload" href="<?php echo esc_url( get_theme_file_uri( 'assets/fonts/inter-roman.woff2' ) ); ?>" as="font" type="font/woff2" crossorigin>
		<?php
	}

endif;

add_action( 'wp_head', 'realome_preload_webfonts' );

if ( ! function_exists( 'realome_get_animation_styles' ) ) :

	/**
	 * Get animation styles for GSAP animations.
	 * Called by function realome_scripts() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_animation_styles() {

		return "
		/* GSAP Button Animation Enhancements */

		/* Smooth transitions for buttons only */
		.wp-block-button__link,
		.wp-element-button,
		button,
		input[type='submit'],
		input[type='button'],
		.btn,
		a.button {
			transition: all 0.3s ease;
			transform-origin: center;
			will-change: transform, box-shadow;
		}

		/* Button focus states for accessibility */
		.wp-block-button__link:focus,
		.wp-element-button:focus,
		button:focus,
		input[type='submit']:focus,
		input[type='button']:focus {
			outline: 2px solid var(--wp--preset--color--primary, #63D070);
			outline-offset: 2px;
		}

		/* Reduce motion for users who prefer it */
		@media (prefers-reduced-motion: reduce) {
			.wp-block-button__link,
			.wp-element-button,
			button,
			input[type='submit'],
			input[type='button'],
			.btn,
			a.button {
				transition-duration: 0.01ms !important;
			}
		}

		/* Enhanced button styles with liquid effect support */
		.wp-block-button__link,
		.wp-element-button,
		button,
		input[type='submit'],
		input[type='button'],
		.btn,
		a.button {
			position: relative;
			overflow: hidden;
			border-radius: 8px;
			border: none;
			padding: 12px 24px;
			font-weight: 600;
			text-decoration: none;
			display: inline-block;
		}

		/* Button content wrapper */
		.button-content {
			position: relative;
			z-index: 2;
			pointer-events: none;
		}

		/* Navigation Link Styles */
		.wp-block-navigation-item a,
		.wp-block-navigation-link a,
		nav a:not(.wp-block-button__link):not(.wp-element-button),
		.navigation a:not(.wp-block-button__link):not(.wp-element-button),
		.menu a:not(.wp-block-button__link):not(.wp-element-button) {
			position: relative;
			text-decoration: none !important;
			transition: color 0.3s ease;
			display: inline-block;
			outline: none !important;
			box-shadow: none !important;
		}

		/* Remove default WordPress navigation underlines */
		.wp-block-navigation a,
		.wp-block-navigation-item a,
		.wp-block-navigation-link a {
			text-decoration: none !important;
			border-bottom: none !important;
		}

		/* Navigation Underline */
		.nav-underline {
			position: absolute;
			bottom: -2px;
			left: 0;
			right: 0;
			height: 2px;
			background: var(--wp--preset--color--foreground, #000);
			transform-origin: left center;
			transform: scaleX(0);
		}

		/* Active Navigation State */
		.nav-active,
		.wp-block-navigation-item.current-menu-item a,
		.wp-block-navigation-item.current_page_item a,
		.wp-block-navigation-item.current-page-ancestor a,
		.wp-block-navigation-item--current a,
		.wp-block-navigation-link.current-menu-item a,
		.wp-block-navigation-link.current_page_item a {
			color: var(--wp--preset--color--foreground, #000) !important;
		}

		/* Active Navigation Underlines */
		.nav-active .nav-underline,
		.wp-block-navigation-item.current-menu-item .nav-underline,
		.wp-block-navigation-item.current_page_item .nav-underline,
		.wp-block-navigation-item.current-page-ancestor .nav-underline,
		.wp-block-navigation-item--current .nav-underline,
		.wp-block-navigation-link.current-menu-item .nav-underline,
		.wp-block-navigation-link.current_page_item .nav-underline {
			background: var(--wp--preset--color--foreground, #000);
			transform: scaleX(1);
		}

	


		/* Liquid canvas */
		.liquid-canvas {
			position: absolute;
			top: -50px;
			left: -50px;
			right: -50px;
			bottom: -50px;
			z-index: 1;
			pointer-events: none;
			border-radius: inherit;
		}

		/* Simple button hover effect with scale and background color change */
		.wp-block-button__link,
		.wp-element-button,
		button,
		input[type='submit'],
		input[type='button'],
		.btn,
		a.button {
			transition: all 0.3s ease;
		}

		.wp-block-button__link:hover,
		.wp-element-button:hover,
		button:hover,
		input[type='submit']:hover,
		input[type='button']:hover,
		.btn:hover,
		a.button:hover {
			transform: scale(1.1)!important;
			background-color: #458976ff;
			color: white;
		}

		/* Navigation Focus States for Accessibility - Subtle */
		.wp-block-navigation-item a:focus-visible,
		.wp-block-navigation-link a:focus-visible,
		nav a:focus-visible:not(.wp-block-button__link):not(.wp-element-button),
		.navigation a:focus-visible:not(.wp-block-button__link):not(.wp-element-button),
		.menu a:focus-visible:not(.wp-block-button__link):not(.wp-element-button) {
			outline-offset: 2px;
			color: var(--wp--preset--color--foreground, #000);
		}

		/* Remove focus outline for mouse users */
		.wp-block-navigation-item a:focus:not(:focus-visible),
		.wp-block-navigation-link a:focus:not(:focus-visible),
		nav a:focus:not(:focus-visible):not(.wp-block-button__link):not(.wp-element-button),
		.navigation a:focus:not(:focus-visible):not(.wp-block-button__link):not(.wp-element-button),
		.menu a:focus:not(:focus-visible):not(.wp-block-button__link):not(.wp-element-button) {
			outline: none;
		}

		/* Mobile Navigation Adjustments */
		@media (max-width: 768px) {
			.nav-underline {
				height: 1px;
				bottom: -1px;
			}
		}

		/* Reduced Motion for Navigation */
		@media (prefers-reduced-motion: reduce) {
			.wp-block-navigation-item a,
			.wp-block-navigation-link a,
			nav a:not(.wp-block-button__link):not(.wp-element-button),
			.navigation a:not(.wp-block-button__link):not(.wp-element-button),
			.menu a:not(.wp-block-button__link):not(.wp-element-button) {
				transition: none !important;
			}

			.nav-underline {
				transition: none !important;
			}
		}
		";

	}

endif;

if ( ! function_exists( 'realome_editor_styles' ) ) :

	/**
	 * Get editor styles.
	 * Called by function realome_inline_editor_styles() above.
	 *
	 * @since realome 1.0
	 *
	 * @return string
	 */
	function realome_editor_styles() {

		return '
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h1,
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h2,
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h3,
		.editor-styles-wrapper .block-editor-block-list__layout.is-root-container > p + h4 {
			margin-top: var(--wp--custom--spacing--medium, 6rem) !important;
		}
		
		.nav-list-vertical-gap-small {
			.wp-block-navigation__container {
				row-gap: 10px;
			}
		}

		/* core block fix */
		@media only screen and (min-width: 482px) {
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignleft, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignleft {
			    margin-right: 2em !important;
			}
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignright, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignright {
			    margin-left: 2em !important;
			}
		
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignleft, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignleft { 
				margin-left: calc(50% - 400px) !important;
			}
			.editor-styles-wrapper .edit-post-visual-editor__post-title-wrapper > .alignright, .editor-styles-wrapper .block-editor-block-list__layout.is-root-container > .alignright { 
				margin-right: calc(50% - 400px) !important;
			}

		}
		.wp-block-search__input {
			border-color: var(--wp--preset--color--foreground);
			border-width: 0.125rem;
			border-radius: 0.5rem;
			padding: calc(calc(0.551em + 2px) - 2px) calc(calc(1.5rem + 2px) - 2px);
		}
		.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
			background-color: var(--wp--preset--color--white);
			border-color: var(--wp--preset--color--foreground);
			border-width: 0.125rem;
			border-radius: 0.5rem;
		}
		.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper input.wp-block-search__input {
			padding: calc(calc(0.551em + 2px) - 2px) calc(calc(1.5rem + 2px) - 2px);
		}
		
		table.mce-item-table {
		    border-collapse: collapse;
		    width: 100%;
		}
		.mce-item-table thead {
			border-bottom: 3px solid;
		}
		.mce-item-table tfoot {
			border-top: 3px solid;
		}
		.mce-item-table td,
		.mce-item-table th {
			padding: 0.5em;
			border: 1px solid;
			word-break: normal;
		}
		.mce-item-table figcaption {
			color: #555;
			font-size: 13px;
			text-align: center;
		}
		.is-dark-theme .mce-item-table figcaption {
			color: rgba(255, 255, 255, 0.65);
		}
		.wp-block-freeform.block-library-rich-text__tinymce a {
			color: var(--wp--preset--color--foreground);
		}
		';

	}

endif;

// Load the TGMPA class.
require get_parent_theme_file_path( '/inc/plugins.php' );

// Add block patterns.
require get_template_directory() . '/inc/block-patterns.php';

/**
 * Disable comments functionality
 */
function realome_disable_comments() {
    // Close comments on the front-end
    add_filter('comments_open', '__return_false', 20, 2);
    add_filter('pings_open', '__return_false', 20, 2);

    // Hide existing comments
    add_filter('comments_array', '__return_empty_array', 10, 2);

    // Remove comments page from admin menu
    add_action('admin_menu', function() {
        remove_menu_page('edit-comments.php');
    }, 999);

    // Remove comments links from admin bar
    add_action('wp_before_admin_bar_render', function() {
        global $wp_admin_bar;
        if ($wp_admin_bar) {
            $wp_admin_bar->remove_menu('comments');
        }
    });

    // Remove comments from post and pages
    add_action('init', function() {
        remove_post_type_support('post', 'comments');
        remove_post_type_support('page', 'comments');
    });

    // Remove comments from admin menu
    add_action('admin_init', function() {
        global $menu;
        if (is_array($menu)) {
            foreach ($menu as $key => $item) {
                if (isset($item[2]) && $item[2] === 'edit-comments.php') {
                    unset($menu[$key]);
                }
            }
        }
    });
}
add_action('init', 'realome_disable_comments');

/**
 * Enable Customizer for block themes
 */
function realome_enable_customizer() {
    // Remove the theme.json to allow Customizer (optional - only if you want full Customizer functionality)
    // This is commented out by default to preserve block theme functionality
    // remove_theme_support( 'block-templates' );

    // Ensure Customizer is available
    add_action( 'customize_register', function( $wp_customize ) {
        // Add a basic section if none exist
        if ( empty( $wp_customize->sections() ) ) {
            $wp_customize->add_section( 'realome_basic_settings', array(
                'title'    => __( 'Basic Theme Settings', 'realome' ),
                'priority' => 30,
            ) );
        }
    } );
}
add_action( 'after_setup_theme', 'realome_enable_customizer' );

if ( ! function_exists( 'realome_get_cursor_tracking_styles' ) ) :

	/**
	 * Get cursor tracking styles.
	 * Called by function realome_scripts() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_cursor_tracking_styles() {

		return "
		/* Realome Cursor Tracking Styles */

		/* Cursor tracking images */
		.cursor-track-image {
			transition: none !important;
			will-change: transform;
		}

		/* Parallax cursor tracking */
		.parallax-cursor-track {
			transition: none !important;
			will-change: transform;
		}

		/* Magnetic cursor effect */
		.magnetic-cursor {
			transition: none !important;
			will-change: transform;
			cursor: pointer;
		}

		/* Smooth cursor tracking for hero images */
		.wp-block-cover img,
		.wp-block-media-text img,
		.hero img,
		.hero-section img {
			will-change: transform;
		}

		/* Performance optimizations */
		.cursor-track-image,
		.parallax-cursor-track,
		.magnetic-cursor {
			backface-visibility: hidden;
			perspective: 1000px;
		}

		/* Disable cursor tracking on mobile for performance */
		@media (max-width: 768px) {
			.cursor-track-image,
			.parallax-cursor-track,
			.magnetic-cursor {
				will-change: auto;
				transform: none !important;
			}
		}

		/* Reduced motion support */
		@media (prefers-reduced-motion: reduce) {
			.cursor-track-image,
			.parallax-cursor-track,
			.magnetic-cursor {
				will-change: auto;
				transform: none !important;
			}
		}

		/* Homepage specific enhancements */
		.home .wp-block-cover,
		.front-page .wp-block-cover {
			overflow: hidden;
		}

		.home .wp-block-cover img,
		.front-page .wp-block-cover img {
			transform-origin: center center;
		}
		";

	}

endif;

if ( ! function_exists( 'realome_get_gform_styles' ) ) :

	/**
	 * Get Gravity Forms styles.
	 * Called by function realome_scripts() above.
	 *
	 * @since Realome 1.0
	 *
	 * @return string
	 */
	function realome_get_gform_styles() {

		return "
		/* Realome Gravity Forms Styles */

		/* GForm Submit Buttons */
		.gform_wrapper .gform_footer input[type='submit'],
		.gform_wrapper .gform_footer button[type='submit'],
		.gform_wrapper .gform_page_footer input[type='submit'],
		.gform_wrapper .gform_page_footer button[type='submit'],
		.gform_wrapper input[type='submit'],
		.gform_wrapper button[type='submit'] {
			background-color: #8e430d !important;
			background: #8e430d !important;
			border: none !important;
			border-radius: 8px !important;
			color: #ffffff !important;
			padding: 12px 24px !important;
			font-weight: 600 !important;
			font-size: 1rem !important;
			cursor: pointer !important;
			transition: all 0.3s ease !important;
			text-decoration: none !important;
			display: inline-block !important;
			position: relative !important;
			overflow: hidden !important;
		}

		/* GForm Button Hover Effect */
		.gform_wrapper .gform_footer input[type='submit']:hover,
		.gform_wrapper .gform_footer button[type='submit']:hover,
		.gform_wrapper .gform_page_footer input[type='submit']:hover,
		.gform_wrapper .gform_page_footer button[type='submit']:hover,
		.gform_wrapper input[type='submit']:hover,
		.gform_wrapper button[type='submit']:hover {
			background-color: #b8540f !important;
			background: #b8540f !important;
			transform: translateY(-2px) !important;
			box-shadow: 0 8px 25px rgba(142, 67, 13, 0.3) !important;
		}

		/* GForm Button Active/Focus States */
		.gform_wrapper .gform_footer input[type='submit']:active,
		.gform_wrapper .gform_footer button[type='submit']:active,
		.gform_wrapper input[type='submit']:active,
		.gform_wrapper button[type='submit']:active {
			transform: translateY(0) !important;
			box-shadow: 0 4px 15px rgba(142, 67, 13, 0.2) !important;
		}

		.gform_wrapper .gform_footer input[type='submit']:focus,
		.gform_wrapper .gform_footer button[type='submit']:focus,
		.gform_wrapper input[type='submit']:focus,
		.gform_wrapper button[type='submit']:focus {
			outline: 2px solid #8e430d !important;
			outline-offset: 2px !important;
		}

		/* Remove borders from text inputs and textareas */
		.gform_wrapper input[type='text'],
		.gform_wrapper input[type='email'],
		.gform_wrapper input[type='tel'],
		.gform_wrapper input[type='url'],
		.gform_wrapper input[type='password'],
		.gform_wrapper input[type='number'],
		.gform_wrapper input[type='date'],
		.gform_wrapper input[type='time'],
		.gform_wrapper input[type='datetime-local'],
		.gform_wrapper input[type='search'],
		.gform_wrapper textarea,
		.gform_wrapper select {
			border: none !important;
			border-radius: 8px !important;
			background-color: #f8f9fa !important;
			padding: 12px 16px !important;
			font-size: 1rem !important;
			transition: all 0.3s ease !important;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
		}

		/* Input focus states */
		.gform_wrapper input[type='text']:focus,
		.gform_wrapper input[type='email']:focus,
		.gform_wrapper input[type='tel']:focus,
		.gform_wrapper input[type='url']:focus,
		.gform_wrapper input[type='password']:focus,
		.gform_wrapper input[type='number']:focus,
		.gform_wrapper input[type='date']:focus,
		.gform_wrapper input[type='time']:focus,
		.gform_wrapper input[type='datetime-local']:focus,
		.gform_wrapper input[type='search']:focus,
		.gform_wrapper textarea:focus,
		.gform_wrapper select:focus {
			outline: none !important;
			background-color: #ffffff !important;
			box-shadow: 0 4px 15px rgba(142, 67, 13, 0.1) !important;
			transform: translateY(-1px) !important;
		}

		/* Remove default GForm styling */
		.gform_wrapper .gform_footer {
			padding: 16px 0 !important;
		}

		.gform_wrapper .gfield {
			margin-bottom: 20px !important;
		}

		.gform_wrapper .gfield_label {
			font-weight: 600 !important;
			margin-bottom: 8px !important;
			color: #333333 !important;
		}

		/* Validation styling */
		.gform_wrapper .gfield_error input,
		.gform_wrapper .gfield_error textarea,
		.gform_wrapper .gfield_error select {
			background-color: #fef2f2 !important;
			box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1) !important;
		}

		.gform_wrapper .validation_error {
			background-color: #fef2f2 !important;
			border: 1px solid #fca5a5 !important;
			border-radius: 8px !important;
			padding: 12px 16px !important;
			margin-bottom: 20px !important;
		}

		/* Success message styling */
		.gform_wrapper .gform_confirmation_message {
			background-color: #f0fdf4 !important;
			border: 1px solid #86efac !important;
			border-radius: 8px !important;
			padding: 16px 20px !important;
			color: #166534 !important;
		}

		/* File upload styling */
		.gform_wrapper input[type='file'] {
			background-color: #f8f9fa !important;
			border: 2px dashed #d1d5db !important;
			border-radius: 8px !important;
			padding: 20px !important;
			text-align: center !important;
		}

		/* Checkbox and radio styling */
		.gform_wrapper .gfield_checkbox input[type='checkbox'],
		.gform_wrapper .gfield_radio input[type='radio'] {
			margin-right: 8px !important;
			transform: scale(1.2) !important;
		}

		/* Multi-page form navigation */
		.gform_wrapper .gform_page_footer .gform_next_button,
		.gform_wrapper .gform_page_footer .gform_previous_button {
			background-color: #6b7280 !important;
			margin-right: 10px !important;
		}

		.gform_wrapper .gform_page_footer .gform_next_button:hover {
			background-color: #8e430d !important;
		}

		/* Responsive adjustments */
		@media (max-width: 768px) {
			.gform_wrapper input[type='submit'],
			.gform_wrapper button[type='submit'] {
				width: 100% !important;
				padding: 14px 20px !important;
			}

			.gform_wrapper input,
			.gform_wrapper textarea,
			.gform_wrapper select {
				padding: 14px 16px !important;
				font-size: 16px !important; /* Prevents zoom on iOS */
			}
		}

		/* Reduced motion support */
		@media (prefers-reduced-motion: reduce) {
			.gform_wrapper input,
			.gform_wrapper textarea,
			.gform_wrapper select,
			.gform_wrapper input[type='submit'],
			.gform_wrapper button[type='submit'] {
				transition: none !important;
				transform: none !important;
			}
		}
		";

	}

endif;

add_filter( 'gform_required_legend', '__return_empty_string' );