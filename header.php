<!doctype html>
<html lang="en">

<head>
    <meta name="robots" content="noindex">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/theme.min.css">
    <title>Superangel</title>
    <link rel="icon" type="image/x-icon" href="assets/images/superangel-favicon.png">
<!-- Global site tag (gtag.js) - Google Analytics -->
<!-- <script async src="https://www.googletagmanager.com/gtag/js?id=UA-124642361-1"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-124642361-1');
</script> -->

</head>

<body>
<div class="cursor"></div>
<div id="smooth-wrapper">
    <div id="smooth-content">

    <?php
    $fileName = $_SERVER['PHP_SELF'];
    $footerAnimeClass = ' super_footer_anime';
    switch (true) {
        case stristr($fileName,'index.php'):
            $pageClass = 'bg-yellow';
            $bgImgAnimateClass ='';
        break;
        case stristr($fileName,'portfolio.php'):
            $pageClass = 'portfolio-bg-img';
            $bgImgAnimateClass = ' header-follow-img';
            $headerImage = 'style="background: url("../images/portfolio/port_head_img.jpg");';
        break;
        case stristr($fileName,'team.php'):
            $pageClass = 'team-header';
            $bgImgAnimateClass = ' header-follow-img';
            $headerImage = 'style="background: url("../images/team/header-bg.jpg");';
        break;
        case stristr($fileName,'why.php'):
            $pageClass = 'why-header';
            $bgImgAnimateClass = ' header-follow-img';
            $headerImage = 'style="background: url("../images/team/header-bg.jpg");';
            $footerAnimeClass = '';
        break;
        case stristr($fileName,'privacy-policy.php'):
            $pageClass = 'bg-yellow';
            $bgImgAnimateClass ='';
        break;
        case stristr($fileName,'esg-policy.php'):
            $pageClass = 'bg-yellow';
            $bgImgAnimateClass ='';
        break;
        default:
            $pageClass = '';
            $headerImage = '';
            $bgImgAnimateClass ='';
    }
    ?>

    <header class="<?php echo $pageClass, $bgImgAnimateClass;?>">
        <nav class="navbar">
            <div class="container-fluid header-container js-cursor">
                <!-- <a class="navbar-brand js-cursor-links" href="/super"> -->
                <a class="navbar-brand js-cursor-links" href="/">
                <svg width="163px" height="24px" viewBox="0 0 163 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                        <g id="Group" transform="translate(0.992188, 0.000000)" fill="#1F1F27">
                            <path d="M4.1293525,8.17908663 C3.3137925,8.18053997 2.5161325,7.94192997 1.8373125,7.49344997 C1.1584825,7.04496997 0.6290135,6.40677997 0.3158975,5.65963997 C0.00278077968,4.91249997 -0.0799057003,4.08999997 0.0783017997,3.29622997 C0.2365095,2.50244997 0.6284995,1.77306997 1.2046625,1.20040997 C1.7808325,0.627735969 2.5152725,0.237513969 3.3150525,0.0791210691 C4.1148325,-0.0792719309 4.9439925,0.00128400908 5.6976125,0.310594969 C6.4512325,0.619904969 7.0954225,1.14406997 7.5486825,1.81674997 C8.0019325,2.48942997 8.2438625,3.28038997 8.2438625,4.08953997 C8.2419125,5.17227997 7.8080325,6.21023997 7.0370425,6.97653997 C6.2660525,7.74284997 5.2206625,8.17518997 4.1293525,8.17908663 Z M4.1293525,1.79468997 C3.6718725,1.79468997 3.2246725,1.92927997 2.8443025,2.18143997 C2.4639225,2.43359997 2.1674625,2.79200997 1.9923925,3.21133997 C1.8173225,3.63066997 1.7715125,4.09207997 1.8607625,4.53723997 C1.9500125,4.98239997 2.1703125,5.39129997 2.4937925,5.71223997 C2.8172725,6.03317997 3.2294125,6.25174997 3.6781025,6.34028997 C4.1267825,6.42883997 4.5918525,6.38339997 5.0145025,6.20969997 C5.4371525,6.03600997 5.7984025,5.74187997 6.0525625,5.36448997 C6.3067225,4.98709997 6.4423725,4.54341997 6.4423725,4.08953997 C6.4404225,3.48149997 6.1961025,2.89891997 5.7627425,2.46897997 C5.3293925,2.03902997 4.7422025,1.79662997 4.1293525,1.79468997 Z" id="Shape" fill-rule="nonzero"></path>
                            <path d="M14.3736125,22.9926 C12.7759125,23.0023 11.2027125,22.602 9.8069125,21.8304 C9.5791825,21.6978 9.3589225,21.5529 9.1471125,21.3965 C9.0931725,21.3647 9.0466325,21.3219 9.0105525,21.2709 C8.9744725,21.22 8.9496625,21.162 8.9377625,21.1009 C8.9258625,21.0398 8.9271325,20.9768 8.9414925,20.9162 C8.9558525,20.8556 8.9829725,20.7987 9.0210825,20.7492 C9.4139925,20.1093 9.8069125,19.4694 10.2146125,18.8442 C10.3703125,18.5941 10.5631125,18.5721 10.8226125,18.7559 C11.3858125,19.167 12.0081125,19.4918 12.6685125,19.7195 C13.3981125,19.9643 14.1680125,20.0691 14.9371125,20.0284 C15.1693125,20.0142 15.3986125,19.9697 15.6191125,19.896 C15.8225125,19.8258 16.0035125,19.7033 16.1436125,19.541 C16.2836125,19.3786 16.3776125,19.1823 16.4161125,18.9721 C16.4546125,18.7618 16.4360125,18.5452 16.3624125,18.3444 C16.2888125,18.1436 16.1628125,17.9658 15.9972125,17.8292 C15.8432125,17.7014 15.6708125,17.5973 15.4857125,17.5202 C14.9445125,17.2996 14.3959125,17.1083 13.8473125,16.9024 C13.1059125,16.645 12.4239125,16.4096 11.7344125,16.1154 C11.3615125,15.9473 11.0105125,15.7349 10.6891125,15.4828 C10.2103125,15.1216 9.8374625,14.64 9.6093425,14.0878 C9.3812325,13.5355 9.3061025,12.9328 9.3917525,12.3421 C9.4507725,11.4569 9.8035525,10.6161 10.3950125,9.95073997 C10.9864125,9.28542997 11.7833125,8.83313997 12.6611125,8.66446997 C13.7743125,8.34873997 14.9411125,8.26350997 16.0889125,8.41407997 C17.2367125,8.56465997 18.3411125,8.94781997 19.3333125,9.53974997 C19.8004125,9.81189997 19.8226125,9.92957997 19.5483125,10.393 C19.2740125,10.8563 18.9033125,11.4521 18.5845125,11.9817 C18.3844125,12.3127 18.1990125,12.3495 17.8432125,12.1582 C16.7643125,11.4966 15.4935125,11.2118 14.2328125,11.3492 C13.9074125,11.3896 13.5942125,11.4973 13.3135125,11.6654 C13.1802125,11.7481 13.0703125,11.863 12.9941125,11.9994 C12.9179125,12.1358 12.8780125,12.2892 12.8780125,12.4451 C12.8780125,12.601 12.9179125,12.7544 12.9941125,12.8908 C13.0703125,13.0272 13.1802125,13.1421 13.3135125,13.2248 C13.6511125,13.4235 14.0090125,13.5862 14.3811125,13.7102 C14.9519125,13.9162 15.5376125,14.0853 16.1158125,14.2839 C16.5681125,14.4531 17.0129125,14.6223 17.4503125,14.8209 C18.2131125,15.1243 18.8591125,15.6605 19.2948125,16.352 C19.7305125,17.0434 19.9335125,17.8543 19.8745125,18.6677 C19.8894125,19.5009 19.6436125,20.3182 19.1709125,21.0071 C18.6983125,21.696 18.0220125,22.2226 17.2353125,22.5145 C16.6929125,22.7048 16.1347125,22.8476 15.5672125,22.9411 C15.1707125,22.981 14.7722125,22.9982 14.3736125,22.9926 Z" id="Path"></path>
                            <path d="M33.2432125,13.0778 L33.2432125,9.06915997 C33.2432125,8.64255997 33.3469125,8.53222997 33.7918125,8.53222997 L36.1493125,8.53222997 C36.5792125,8.53222997 36.6831222,8.64255997 36.6831222,9.06915997 L36.6831222,16.6819 C36.6880125,17.5967 36.5221125,18.5044 36.1937125,19.3592 C35.9529125,20.0375 35.5700125,20.6575 35.0704125,21.1785 C34.5709125,21.6995 33.9658125,22.1097 33.2950125,22.3822 C31.5005125,23.1296 29.4941125,23.2079 27.6459125,22.6029 C26.6711125,22.324 25.7970125,21.7741 25.1270125,21.0181 C24.4571125,20.2622 24.0191125,19.3318 23.8650125,18.3368 C23.7439125,17.7362 23.6819125,17.1253 23.6797125,16.5127 C23.6797125,14.034 23.6797125,11.5553 23.6797125,9.06915997 C23.6797125,8.64255997 23.7835125,8.53222997 24.2135125,8.53222997 L26.5932125,8.53222997 C27.0084125,8.53222997 27.1121125,8.64255997 27.1121125,9.06180997 C27.1121125,11.6214 27.1121125,14.2105 27.1121125,16.7481 C27.1015125,17.2645 27.1973125,17.7776 27.3939125,18.2559 C27.6194125,18.7898 28.0085125,19.2398 28.5062125,19.5421 C29.0038125,19.8444 29.5847125,19.9837 30.1665125,19.9403 C30.5654125,19.9703 30.9661125,19.9169 31.3428125,19.7834 C31.7194125,19.6499 32.0636125,19.4394 32.3530125,19.1655 C32.6424125,18.8915 32.8705125,18.5603 33.0226125,18.1933 C33.1747125,17.8263 33.2473125,17.4316 33.2357125,17.0349 C33.2802125,15.711 33.2357125,14.3944 33.2357125,13.0778 L33.2432125,13.0778 Z" id="Path"></path>
                            <path d="M41.2336125,15.6521 L41.2336125,9.09857997 C41.2336125,8.63519997 41.3299125,8.53222997 41.7970125,8.53222997 C43.5318125,8.53222997 45.2739125,8.53222997 46.9865125,8.53222997 C48.0429125,8.55384997 49.0763125,8.84244997 49.9890125,9.37072997 C51.1239125,10.0491 51.9544125,11.1326 52.3100125,12.399 C52.6656125,13.6654 52.5195125,15.0188 51.9017125,16.1817 C51.3658125,17.1639 50.5002125,17.929 49.4552125,18.3442 C48.4397125,18.7672 47.3463125,18.9751 46.2451125,18.9547 C45.8077125,18.9547 45.3629125,18.9547 44.9255125,18.9547 C44.6957125,18.9547 44.6512125,19.0282 44.6512125,19.2415 C44.6512125,20.2419 44.6512125,21.2422 44.6512125,22.2498 C44.6512125,22.6765 44.5548125,22.7794 44.1100125,22.7794 C43.3687125,22.7794 42.5458125,22.7794 41.7599125,22.7794 C41.3225125,22.7794 41.2187125,22.6691 41.2187125,22.2425 L41.2336125,15.6521 Z M44.6660125,11.5258 C44.6660125,12.9969 44.6660125,14.3944 44.6660125,15.8066 C44.6660125,15.8581 44.7772125,15.939 44.8365125,15.939 C45.4593125,15.939 46.0820125,15.939 46.7048125,15.939 C47.3034125,15.9156 47.8739125,15.6809 48.3135125,15.277 C48.5518125,15.0542 48.7339125,14.7788 48.8448125,14.4731 C48.9557125,14.1674 48.9925125,13.84 48.9520125,13.5176 C48.9116125,13.1952 48.7951125,12.8868 48.6120125,12.6174 C48.4290125,12.348 48.1844125,12.1252 47.8983125,11.9671 C46.8975125,11.3419 45.7855125,11.5847 44.6660125,11.5258 Z" id="Shape"></path>
                            <path d="M59.5571125,11.5258 L59.5571125,14.1149 L64.8281125,14.1149 C65.3619125,14.1149 65.4434125,14.1958 65.4434125,14.718 L65.4434125,16.5569 C65.4434125,17.0129 65.3470125,17.1085 64.8726125,17.1085 L59.5645125,17.1085 L59.5645125,19.8079 L59.8684125,19.8079 L65.7400125,19.8079 C66.2367125,19.8079 66.3182125,19.8888 66.3182125,20.389 C66.3182125,21.0289 66.3182125,21.6614 66.3182125,22.3013 C66.3182125,22.6544 66.2070125,22.7794 65.8512125,22.7794 L56.5916125,22.7794 C56.2284125,22.7794 56.1172125,22.6691 56.1172125,22.3087 L56.1172125,9.01031997 C56.1172125,8.62784997 56.2358125,8.53222997 56.6658125,8.53222997 L65.7326125,8.53222997 C66.1106125,8.53222997 66.2218125,8.65726997 66.2218125,9.02502997 L66.2218125,11.033 C66.2218125,11.4155 66.1032125,11.5332 65.7177125,11.5332 L59.5571125,11.5332 L59.5571125,11.5258 Z" id="Path"></path>
                            <path d="M79.1808125,18.263 C79.2327125,18.3292 79.2846125,18.4101 79.3365125,18.4911 L81.9313125,22.1687 C81.9919125,22.2423 82.0418125,22.324 82.0795125,22.4114 C82.0984125,22.445 82.1083125,22.4827 82.1085156,22.5211 C82.1087125,22.5596 82.0991125,22.5974 82.0806125,22.6312 C82.0621125,22.6649 82.0354125,22.6935 82.0028125,22.7142 C81.9702125,22.7349 81.9328125,22.7472 81.8942125,22.7498 C81.8081125,22.761 81.7208125,22.761 81.6347125,22.7498 L78.7953125,22.7498 C78.6553125,22.7591 78.5155125,22.7288 78.3921125,22.6624 C78.2688125,22.596 78.1669125,22.4962 78.0985125,22.3746 C77.3571125,21.2934 76.6158125,20.2269 75.8744125,19.153 C75.8314125,19.0738 75.7651125,19.0095 75.6843125,18.9688 C75.6034125,18.928 75.5120125,18.9128 75.4222125,18.925 C75.0268125,18.925 74.6265125,18.925 74.2212125,18.925 C73.9988125,18.925 73.9246125,18.9765 73.9246125,19.2119 C73.9246125,20.2122 73.9246125,21.2125 73.9246125,22.2128 C73.9246125,22.6321 73.8209125,22.7424 73.4057125,22.7424 C72.6050125,22.7424 71.8044125,22.7424 70.9963125,22.7424 C70.5960125,22.7424 70.4922125,22.6321 70.4922125,22.2275 L70.4922125,8.98800997 C70.4922125,8.59817997 70.5960125,8.47313997 70.9815125,8.47313997 C72.7755125,8.47313997 74.5696125,8.47313997 76.3637125,8.47313997 C77.5683125,8.49103997 78.7401125,8.86492997 79.7294125,9.54701997 C80.3363125,9.99350997 80.8375125,10.5661 81.1982125,11.2247 C81.5589125,11.8833 81.7702125,12.6122 81.8175125,13.3602 C81.8648125,14.1083 81.7469125,14.8576 81.4721125,15.5558 C81.1972125,16.254 80.7720125,16.8842 80.2262125,17.4025 C79.8929125,17.707 79.5440125,17.9942 79.1808125,18.263 Z M73.9914125,13.7469 L73.9914125,15.8137 C73.9914125,15.9388 73.9914125,16.027 74.1693125,16.027 C74.8217125,16.027 75.4815125,16.027 76.1339125,15.9903 C76.6764125,15.9697 77.1972125,15.7734 77.6166125,15.4313 C77.8858125,15.1916 78.0920125,14.8903 78.2174125,14.5537 C78.3427125,14.2171 78.3834125,13.8552 78.3360125,13.4994 C78.2885125,13.1436 78.1543125,12.8047 77.9450125,12.512 C77.7356125,12.2193 77.4575125,11.9817 77.1347125,11.8198 C76.7570125,11.6526 76.3534125,11.5506 75.9411125,11.5182 C75.3777125,11.4594 74.8069125,11.5182 74.2360125,11.4815 C74.0507125,11.4815 74.0062125,11.5477 74.0136125,11.7168 L73.9914125,13.7469 Z" id="Shape"></path>
                            <path d="M96.9738125,22.8011 C96.5660125,22.8011 96.1583125,22.8011 95.7505125,22.8011 C95.3428125,22.8011 95.2316125,22.735 95.0833125,22.3598 C94.8239125,21.7273 94.5644125,21.0947 94.3420125,20.4548 C94.3247125,20.392 94.2852125,20.3374 94.2308125,20.3009 C94.1764125,20.2645 94.1106125,20.2486 94.0454125,20.2562 L88.8930125,20.2562 C88.8268125,20.249 88.7602125,20.2659 88.7056125,20.3038 C88.6511125,20.3417 88.6123125,20.398 88.5965125,20.4622 C88.3370125,21.1094 88.0553125,21.7493 87.7958125,22.404 C87.7547125,22.5291 87.6725125,22.637 87.5623125,22.7104 C87.4521125,22.7837 87.3202125,22.8183 87.1879125,22.8085 L84.6154125,22.8085 C84.1780125,22.8085 84.0742125,22.6393 84.2596125,22.2421 C86.2365125,17.829 88.2134125,13.3937 90.1904125,8.93641997 C90.2293125,8.81831997 90.3062125,8.71616997 90.4094125,8.64572997 C90.5125125,8.57528997 90.6362125,8.54044997 90.7612125,8.54658997 L92.1327125,8.54658997 C92.2566125,8.54116997 92.3790125,8.57542997 92.4818125,8.64429997 C92.5846125,8.71316997 92.6624125,8.81299997 92.7036125,8.92906997 L98.6344125,22.2348 C98.8198125,22.6467 98.7160125,22.8085 98.2563125,22.8085 C97.7967125,22.8085 97.4038125,22.8011 96.9738125,22.8011 Z M89.7678125,17.3877 L93.1410125,17.3877 L91.4433125,13.379 C90.8724125,14.7103 90.3313125,16.0416 89.7678125,17.3803 L89.7678125,17.3877 Z" id="Shape"></path>
                            <path d="M105.280812,14.4754 L105.280812,22.2205 C105.280812,22.7133 105.198812,22.7942 104.694812,22.7942 C103.953812,22.7942 103.211812,22.7942 102.426812,22.7942 C102.040812,22.7942 101.929812,22.6839 101.929812,22.2941 L101.929812,8.89273997 C101.929812,8.50290997 102.033812,8.39257997 102.426812,8.39257997 C102.818812,8.39257997 103.048812,8.39257997 103.367812,8.39257997 C103.508812,8.39320997 103.647812,8.42372997 103.775812,8.48208997 C103.903812,8.54044997 104.016812,8.62528997 104.108812,8.73091997 L111.626812,16.7114 C111.681812,16.7783 111.731812,16.8496 111.774812,16.9247 L111.856812,16.8659 L111.856812,9.07661997 C111.856812,8.64265997 111.959812,8.53232997 112.389812,8.53232997 L114.695812,8.53232997 C115.125812,8.53232997 115.221812,8.63529997 115.221812,9.05454997 L115.221812,22.4559 C115.221812,22.8752 115.125812,22.9781 114.702812,22.9781 L113.791812,22.9781 C113.650812,22.9845 113.510812,22.9586 113.382812,22.9025 C113.253812,22.8463 113.139812,22.7615 113.049812,22.6545 L105.562812,14.6666 C105.493812,14.5915 105.429812,14.513 105.369812,14.4313 L105.280812,14.4754 Z" id="Path"></path>
                            <path d="M132.472812,18.3884 C132.472812,19.1975 132.472812,19.9992 132.472812,20.8009 C132.486812,20.9433 132.461812,21.0868 132.399812,21.2163 C132.338812,21.3457 132.243812,21.4563 132.123812,21.5365 C131.527812,21.9826 130.857812,22.3234 130.144812,22.5441 C128.363812,23.1142 126.456812,23.1728 124.643812,22.7133 C123.003812,22.2951 121.564812,21.318 120.578812,19.9531 C119.591812,18.5883 119.119812,16.9226 119.246812,15.2477 C119.332812,13.5693 120.010812,11.9745 121.162812,10.7427 C122.315812,9.51080997 123.867812,8.72050997 125.547812,8.51022997 C127.353812,8.20371997 129.209812,8.42080997 130.893812,9.13542997 C131.195812,9.26348997 131.486812,9.41858997 131.760812,9.59881997 C131.809812,9.62008997 131.852812,9.65207997 131.886812,9.69235997 C131.920812,9.73263997 131.944812,9.78012997 131.957812,9.83121997 C131.970812,9.88230997 131.971812,9.93563997 131.960812,9.98713997 C131.949812,10.0386 131.926812,10.087 131.893812,10.1284 L130.804812,12.0923 C130.655812,12.3423 130.500812,12.3571 130.181812,12.2467 C129.621812,12.037 129.049812,11.8602 128.468812,11.7171 C127.658812,11.5405 126.818812,11.5405 126.007812,11.7171 C125.021812,11.8821 124.137812,12.4188 123.540812,13.2152 C122.943812,14.0115 122.680812,15.0059 122.804812,15.9906 C122.827812,16.5446 122.961812,17.0885 123.197812,17.5908 C123.434812,18.093 123.769812,18.5435 124.183812,18.9159 C124.597812,19.2884 125.082812,19.5753 125.609812,19.76 C126.136812,19.9447 126.695812,20.0235 127.253812,19.9918 C127.878812,19.9863 128.498812,19.8717 129.084812,19.6535 C129.151812,19.632 129.210812,19.5882 129.249812,19.5293 C129.288812,19.4704 129.305812,19.4001 129.299812,19.3299 C129.299812,18.2119 129.299812,17.0865 129.299812,15.9685 C129.299812,15.5787 129.402812,15.4757 129.795812,15.4683 L131.968812,15.4683 C132.360812,15.4683 132.472812,15.586 132.472812,15.9685 L132.472812,18.3884 Z" id="Path"></path>
                            <path d="M140.056812,11.5258 L140.056812,14.1001 L140.360812,14.1001 L145.312812,14.1001 C145.906812,14.1001 145.972812,14.159 145.972812,14.74 L145.972812,16.6009 C145.972812,16.9834 145.861812,17.0937 145.468812,17.0937 L140.093812,17.0937 L140.093812,19.8593 L146.254812,19.8593 C146.766812,19.8593 146.847812,19.9402 146.847812,20.4551 C146.847812,20.97 146.847812,21.6614 146.847812,22.2645 C146.847812,22.7426 146.758812,22.8309 146.269812,22.8309 L137.973812,22.8309 C137.691812,22.8309 137.410812,22.8309 137.128812,22.8309 C136.720812,22.8309 136.616812,22.7279 136.616812,22.3233 L136.616812,9.08380997 C136.616812,8.64984997 136.713812,8.54687997 137.158812,8.54687997 L146.172812,8.54687997 C146.625812,8.54687997 146.714812,8.64984997 146.714812,9.09851997 C146.714812,9.73842997 146.714812,10.3857 146.714812,11.0256 C146.714812,11.4301 146.610812,11.5405 146.202812,11.5405 L140.056812,11.5405 L140.056812,11.5258 Z" id="Path"></path>
                            <path d="M151.030812,15.6595 L151.030812,9.08386997 C151.030812,8.63519997 151.127812,8.53222997 151.579812,8.53222997 L153.937812,8.53222997 C154.359812,8.53222997 154.463812,8.64255997 154.463812,9.05444997 L154.463812,19.4769 C154.463812,19.8226 154.463812,19.8226 154.797812,19.8226 L160.416812,19.8226 C160.928812,19.8226 161.009812,19.9035 161.009812,20.3963 L161.009812,22.3087 C161.009812,22.6765 160.898812,22.7941 160.520812,22.7941 L151.527812,22.7941 C151.142812,22.7941 151.030812,22.6765 151.030812,22.294 L151.030812,15.6595 Z" id="Path"></path>
                        </g>
                    </g>
                </svg>
                </a>

                <div class="burger-container">
                    <div id="hamburger">
                        <div class="bar topBar"></div>
                        <div class="bar btmBar"></div>
                    </div>
                </div>

                <ul class="navbar-nav js-cursor-links">
                    <li class="nav-item">
                        <?php $active= ($_SERVER['SCRIPT_NAME'] == "/companys.php") ? " active" : "" ;?>
                        <a class="nav-link<?php echo $active;?>" href="portfolio">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <?php $active= ($_SERVER['SCRIPT_NAME'] == "/team.php") ? " active" : "" ;?>
                        <a class="nav-link<?php echo $active;?>" href="team">Team</a>
                    </li>
                    <li class="nav-item">
                        <?php $active= ($_SERVER['SCRIPT_NAME'] == "/why.php") ? " active" : "" ;?>
                        <a class="nav-link<?php echo $active;?>" href="why">Why superangel</a>
                    </li>

                    <li class="nav-item" style="display:none;">
                        <?php $active= ($_SERVER['SCRIPT_NAME'] == "/team.php") ? " active" : "" ;?>
                        <a class="nav-link<?php echo $active;?>" href="team.php">News & Blog</a>
                    </li>


                        <li class="nav-item dropdown position-static">
                            <a class="nav-link dropdown-hover" href=""></a>
                            <div class="dropdown-menu">
                                            <div class="list-group list-group-flush">
                                            <a class="nav-link<?php echo $active;?>" href="team.php">News & Blog</a>
                                            <a class="nav-link<?php echo $active;?>" href="team.php">News & Blog</a>
                                            <a class="nav-link<?php echo $active;?>" href="team.php">News & Blog</a>
                                            </div>
                                      
                            </div>
                        </li>



                </ul>

                <div class="social mobile-menu-social">
                    <a href="https://www.facebook.com/superangel.io/">
                        <img src="assets/images/fecebook.svg" alt="" class="fluid" target="_blank">
                    </a>
                    <a href="https://www.linkedin.com/company/superangel.io/" target="_blank">
                        <img src="assets/images/linked_in.svg" alt="" class="fluid">
                    </a>
                </div>

            </div>
        </nav>
