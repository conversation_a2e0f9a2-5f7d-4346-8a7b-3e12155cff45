/*!
* Project name
* Project Version: 1.0
*/
function cursor(e){mouseCursor.style.top=e.clientY-43.5+"px",mouseCursor.style.left=e.clientX-43.5+"px"}function updateFilters(e){const t=Flip.getState(items),r=filters.filter((t=>e.target)).map((t=>"."+e.target.id)),o=r.length?gsap.utils.toArray(r.join(",")):r;"all"===e.target.id?items.forEach((e=>e.style.display="inline-flex")):items.forEach((e=>e.style.display=-1===o.indexOf(e)?"none":"inline-flex")),Flip.from(t,{duration:.4,scale:!0,absolute:!0,ease:"power1.inOut",onEnter:e=>gsap.fromTo(e,{opacity:0},{opacity:1,duration:1}),onLeave:e=>gsap.to(e,{opacity:0,duration:1})})}jQuery((function(){$(window).width()>700&&$(".super_values .hover-text").hover((function(){var e;$(this).parent().children(".hidden-text").toggleClass("show")})),-1!=navigator.userAgent.indexOf("Safari")&&-1==navigator.userAgent.indexOf("Chrome")||$(window).width()>700&&$(".super_hero_image,.super_hero_title").mousemove((function(e){var t=e.clientX/$(window).width()-.5,r=e.clientY/$(window).height()-.5,o=$(".super-hero-box"),s=$(".content h1");TweenLite.to(o,.6,{rotationY:5*t,rotationX:5*r,ease:Power1.easeOut,transformPerspective:900,transformOrigin:"center"}),TweenLite.to(s,.6,{rotationY:-5*t,rotationX:-5*r,ease:Power1.easeOut,transformPerspective:300,transformOrigin:"center"})}))})),gsap.config({nullTargetWarn:!1});let mouseCursor=document.querySelector(".cursor"),cursorLinks=document.querySelectorAll(".js-cursor-links"),yellowArea=document.querySelectorAll(".js-cursor"),imgFun=document.querySelectorAll(".img-fun");window.addEventListener("mousemove",cursor),cursorLinks.forEach((e=>{e.addEventListener("mouseleave",(()=>{mouseCursor.classList.remove("link-animate"),e.classList.remove("hovered-link")})),e.addEventListener("mouseover",(()=>{mouseCursor.classList.add("link-animate")}))})),yellowArea.forEach((e=>{e.addEventListener("mouseleave",(()=>{mouseCursor.classList.remove("cursor--black"),mouseCursor.classList.remove("cursor--anchor")})),e.addEventListener("mouseover",(e=>{mouseCursor.classList.add("cursor--black")}))})),imgFun.forEach((e=>{e.addEventListener("mouseleave",(()=>{mouseCursor.classList.remove("inverted")})),e.addEventListener("mouseover",(()=>{mouseCursor.classList.add("inverted")}))}));const header=document.querySelector("header"),body=document.querySelector("body"),hamburger=document.querySelector(".burger-container"),navLinks=document.querySelector("nav.navbar"),links=document.querySelectorAll("ul.navbar-nav li"),svgPaths=document.querySelectorAll(".navbar-brand svg path");if(hamburger.addEventListener("click",(e=>{body.classList.toggle("no-scroll"),navLinks.classList.toggle("menu-opened"),svgPaths.forEach((e=>{e.classList.toggle("fade")}))})),gsap.registerPlugin(ScrollTrigger,ScrollSmoother),jQuery(window).width()>992){ScrollSmoother.create({smooth:1.5,effects:!0,normalizeScroll:!0,ignoreMobileResize:!0,smoothTouch:.1}),gsap.set("#super-h1,#super-h2,.super-heading-swoop",{perspective:400});var mySplitText=new SplitText("#super-h1,#super-h2,.super-heading-swoop",{type:"lines"}),lines=mySplitText.lines;function allDone(){mySplitText.revert()}tl=gsap.timeline().from(lines,{duration:2,opacity:0,scale:1,rotationY:-30,transformOrigin:"50% 50%",ease:"back",onComplete:allDone}),tl.play(0);let e=document.querySelectorAll(".super_anim_fade"),t;e&&TweenMax.fromTo(e,1.5,{opacity:0,y:40},{opacity:1,y:0}),document.querySelectorAll(".super_reveal_text_trigger").forEach((e=>{let t=e.querySelectorAll(".super_reveal_text_stagger"),r=gsap.timeline().from(t,{opacity:0,y:100,duration:1.7,ease:"power2.out"},0);ScrollTrigger.create({trigger:e,start:"top 50%",animation:r})}));let r=".follow-bg-img img",o=gsap.utils.mapRange(0,document.body.clientWidth,50,-50),s=gsap.utils.mapRange(0,document.body.clientHeight,50,-50),a=document.querySelector(".header-follow-img");a&&a.addEventListener("mousemove",(e=>{gsap.to(r,{duration:3,overwrite:"auto",x:o(e.clientX),y:s(e.clientY),stagger:.1,ease:"power4.out"})}))}const allCheckbox=document.querySelector("#all"),filters=gsap.utils.toArray(".filter"),items=gsap.utils.toArray(".item");if(filters.forEach((e=>{e.addEventListener("click",(function(t){updateFilters(t),filters.forEach((e=>{e.classList.remove("active")})),e.classList.add("active"),setTimeout((function(){ScrollTrigger.refresh(),console.log("filter scrolltrigger refresh")}),1e3)}))})),document.querySelectorAll(".carousel-box").length){const e=undefined,t=horizontalLoop(gsap.utils.toArray(".carousel-box"),{speed:.5,paused:!1,repeat:-1,paddingRight:20});function horizontalLoop(e,t){function r(e,t){t=t||{},Math.abs(e-u)>s/2&&(e+=e>u?-s:s);let r=gsap.utils.wrap(0,s,e),a=i[r];return a>o.time()!=e>u&&(t.modifiers={time:gsap.utils.wrap(0,o.duration())},a+=o.duration()*(e>u?1:-1)),u=r,t.overwrite=!0,o.tweenTo(a,t)}e=gsap.utils.toArray(e),t=t||{};let o=gsap.timeline({repeat:t.repeat,paused:t.paused,defaults:{ease:"none"},onReverseComplete:()=>o.totalTime(o.rawTime()+100*o.duration())}),s=e.length,a=e[0].offsetLeft,i=[],n=[],l=[],u=0,c=100*(t.speed||1),d=!1===t.snap?e=>e:gsap.utils.snap(t.snap||1),p,m,g,f,v,h;for(gsap.set(e,{xPercent:(e,t)=>{let r=n[e]=parseFloat(gsap.getProperty(t,"width","px"));return l[e]=d(parseFloat(gsap.getProperty(t,"x","px"))/r*100+gsap.getProperty(t,"xPercent")),l[e]}}),gsap.set(e,{x:0}),p=e[s-1].offsetLeft+l[s-1]/100*n[s-1]-a+e[s-1].offsetWidth*gsap.getProperty(e[s-1],"scaleX")+(parseFloat(t.paddingRight)||0),h=0;h<s;h++)v=e[h],m=l[h]/100*n[h],g=v.offsetLeft+m-a,f=g+n[h]*gsap.getProperty(v,"scaleX"),o.to(v,{xPercent:d((m-f)/n[h]*100),duration:f/c},0).fromTo(v,{xPercent:d((m-f+p)/n[h]*100)},{xPercent:l[h],duration:(m-f+p-m)/c,immediateRender:!1},f/c).add("label"+h,g/c),i[h]=g/c;return o.next=e=>r(u+1,e),o.previous=e=>r(u-1,e),o.current=()=>u,o.toIndex=(e,t)=>r(e,t),o.times=i,o.progress(1,!0).progress(0,!0),t.reversed&&(o.vars.onReverseComplete(),o.reverse()),o}}
//# sourceMappingURL=theme.min.js.map