/*!
 * ScrollTrigger 3.10.4
 * https://greensock.com
 * 
 * @license Copyright 2022, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function n(){return De||"undefined"!=typeof window&&(De=window.gsap)&&De.registerPlugin&&De}function r(e,t){return~Se.indexOf(e)&&Se[Se.indexOf(e)+1][t]}function i(e){return!!~ye.indexOf(e)}function u(e,t,n,r,i){return e.addEventListener(t,n,{passive:!r,capture:!!i})}function o(e,t,n,r){return e.removeEventListener(t,n,!!r)}function s(){return Fe&&Fe.isPressed||we.cache++}function a(e,t){function n(r){if(r||0===r){xe&&(de.history.scrollRestoration="manual");var i=Fe&&Fe.isPressed;r=n.v=Math.round(r)||(Fe&&Fe.iOS?1:0),e(r),n.cacheID=we.cache,i&&Ae("ss",r)}else(t||we.cache!==n.cacheID||Ae("ref"))&&(n.cacheID=we.cache,n.v=e());return n.v+n.offset}return n.offset=0,e&&n}function l(e){return De.utils.toArray(e)[0]||("string"==typeof e&&!1!==De.config().nullTargetWarn?console.warn("Element not found:",e):null)}function c(e,t){var n=t.s,u=t.sc,o=we.indexOf(e),s=u===Me.sc?1:2;return~o||(o=we.push(e)-1),we[o+s]||(we[o+s]=a(r(e,n),!0)||(i(e)?u:a((function(t){return arguments.length?e[n]=t:e[n]}))))}function D(e,t,n){function r(e,t){var r=Be();t||a<r-o?(u=i,i=e,s=o,o=r):n?i+=e:i=u+(e-u)/(r-s)*(o-s)}var i=e,u=e,o=Be(),s=o,a=t||50,l=Math.max(500,3*a);return{update:r,reset:function e(){u=i=n?0:i,s=o=0},getVelocity:function e(t){var a=s,c=u,D=Be();return!t&&0!==t||t===i||r(t),o===s||l<D-s?0:(i+(n?c:-c))/((n?D:o)-a)*1e3}}}function f(e,t){return t&&!e._gsapAllow&&e.preventDefault(),e.changedTouches?e.changedTouches[0]:e}function d(e){var t=Math.max.apply(Math,e),n=Math.min.apply(Math,e);return Math.abs(t)>=Math.abs(n)?t:n}function p(){(Ce=De.core.globals().ScrollTrigger)&&Ce.core&&function e(){var t=Ce.core,n=t.bridge||{},r=t._scrollers,i=t._proxies;r.push.apply(r,we),i.push.apply(i,Se),we=r,Se=i,Ae=function e(t,r){return n[t](r)}}()}function h(e){return(De=e||n())&&"undefined"!=typeof document&&document.body&&(de=window,he=(pe=document).documentElement,ge=pe.body,ye=[de,pe,he,ge],De.utils.clamp,ve="onpointerenter"in ge?"pointer":"mouse",me=Pe.isTouch=de.matchMedia&&de.matchMedia("(hover: none), (pointer: coarse)").matches?1:"ontouchstart"in de||0<navigator.maxTouchPoints||0<navigator.msMaxTouchPoints?2:0,Ee=Pe.eventTypes=("ontouchstart"in he?"touchstart,touchmove,touchcancel,touchend":"onpointerdown"in he?"pointerdown,pointermove,pointercancel,pointerup":"mousedown,mousemove,mouseup,mouseup").split(","),setTimeout((function(){return xe=0}),500),p(),fe=1),fe}function g(e){this.init(e)}function m(){return Ue=1}function v(){return Ue=0}function C(e){return e}function y(e){return Math.round(1e5*e)/1e5||0}function F(){return"undefined"!=typeof window}function E(){return Oe||F()&&(Oe=window.gsap)&&Oe.registerPlugin&&Oe}function x(e){return!!~Ye.indexOf(e)}function b(e){return r(e,"getBoundingClientRect")||(x(e)?function(){return en.width=Le.innerWidth,en.height=Le.innerHeight,en}:function(){return Mt(e)})}function w(e,t){var n=t.s,i=t.d2,u=t.d,o=t.a;return(n="scroll"+i)&&(o=r(e,n))?o()-b(e)()[u]:x(e)?(Re[n]||Ie[n])-(Le["inner"+i]||Re["client"+i]||Ie["client"+i]):e[n]-e["offset"+i]}function S(e,t){for(var n=0;n<Je.length;n+=3)t&&!~t.indexOf(Je[n+1])||e(Je[n],Je[n+1],Je[n+2])}function B(e){return"string"==typeof e}function A(e){return"function"==typeof e}function _(e){return"number"==typeof e}function T(e){return"object"==typeof e}function k(e){return A(e)&&e()}function M(e,t){return function(){var n=k(e),r=k(t);return function(){k(n),k(r)}}}function P(e,t,n){return e&&e.progress(t?0:1)&&n&&e.pause()}function O(e,t){if(e.enabled){var n=t(e);n&&n.totalTime&&(e.callbackAnimation=n)}}function N(e){return Le.getComputedStyle(e)}function L(e,t){for(var n in t)n in e||(e[n]=t[n]);return e}function V(e,t){var n=t.d2;return e["offset"+n]||e["client"+n]||0}function R(e){var t,n=[],r=e.labels,i=e.duration();for(t in r)n.push(r[t]/i);return n}function I(e){var t=Oe.utils.snap(e),n=Array.isArray(e)&&e.slice(0).sort((function(e,t){return e-t}));return n?function(e,r,i){var u;if(void 0===i&&(i=.001),!r)return t(e);if(0<r){for(e-=i,u=0;u<n.length;u++)if(n[u]>=e)return n[u];return n[u-1]}for(u=n.length,e+=i;u--;)if(n[u]<=e)return n[u];return n[0]}:function(n,r,i){void 0===i&&(i=.001);var u=t(n);return!r||Math.abs(u-n)<i||u-n<0==r<0?u:t(r<0?n-e:n+e)}}function Y(e,t,n,r){return n.split(",").forEach((function(n){return e(t,n,r)}))}function X(e,t,n,r,i){return e.addEventListener(t,n,{passive:!r,capture:!!i})}function z(e,t,n,r){return e.removeEventListener(t,n,!!r)}function H(e,t,n){return n&&n.wheelHandler&&e(t,"wheel",n)}function W(e,t){if(B(e)){var n=e.indexOf("="),r=~n?(e.charAt(n-1)+1)*parseFloat(e.substr(n+1)):0;~n&&(e.indexOf("%")>n&&(r*=t/100),e=e.substr(0,n-1)),e=r+(e in Nt?Nt[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function j(e,t,n,i,u,o,s,a){var l=u.startColor,c=u.endColor,D=u.fontSize,f=u.indent,d=u.fontWeight,p=Ve.createElement("div"),h=x(n)||"fixed"===r(n,"pinType"),g=-1!==e.indexOf("scroller"),m=h?Ie:n,v=-1!==e.indexOf("start"),C=v?l:c,y="border-color:"+C+";font-size:"+D+";color:"+C+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return y+="position:"+((g||a)&&h?"fixed;":"absolute;"),!g&&!a&&h||(y+=(i===Me?Ct:yt)+":"+(o+parseFloat(f))+"px;"),s&&(y+="box-sizing:border-box;text-align:left;width:"+s.offsetWidth+"px;"),p._isStart=v,p.setAttribute("class","gsap-marker-"+e+(t?" marker-"+t:"")),p.style.cssText=y,p.innerText=t||0===t?e+"-"+t:e,m.children[0]?m.insertBefore(p,m.children[0]):m.appendChild(p),p._offset=p["offset"+i.op.d2],Lt(p,0,i,v),p}function q(){return 34<pt()-gt&&Kt()}function U(){nt&&nt.isPressed&&!(nt.startX>Ie.clientWidth)||(we.cache++,at=at||requestAnimationFrame(Kt),gt||zt("scrollStart"),gt=pt())}function G(){ut=Le.innerWidth,it=Le.innerHeight}function K(){we.cache++,qe||tt||Ve.fullscreenElement||Ve.webkitFullscreenElement||rt&&ut===Le.innerWidth&&!(Math.abs(Le.innerHeight-it)>.25*Le.innerHeight)||Xe.restart(!0)}function Z(e){var t,n=Oe.ticker.frame,r=[],i=0;if(ct!==n||dt){for(jt();i<Xt.length;i+=4)(t=Le.matchMedia(Xt[i]).matches)!==Xt[i+3]&&((Xt[i+3]=t)?r.push(i):jt(1,Xt[i])||A(Xt[i+2])&&Xt[i+2]());for(Wt(),i=0;i<r.length;i++)t=r[i],lt=Xt[t],Xt[t+2]=Xt[t+1](e);lt=0,Ne&&Ut(0,1),ct=n,zt("matchMedia")}}function J(){return z(nn,"scrollEnd",J)||Ut(!0)}function Q(){return we.cache++&&we.forEach((function(e){return"function"==typeof e&&(e.rec=0)}))}function ee(e,t,n,r){if(e.parentNode!==t){for(var i,u=Zt.length,o=t.style,s=e.style;u--;)o[i=Zt[u]]=n[i];o.position="absolute"===n.position?"absolute":"relative","inline"===n.display&&(o.display="inline-block"),s[yt]=s[Ct]=o.flexBasis="auto",o.overflow="visible",o.boxSizing="border-box",o[Ft]=V(e,ke)+kt,o[Et]=V(e,Me)+kt,o[Bt]=s[At]=s.top=s.left="0",Qt(r),s[Ft]=s.maxWidth=n[Ft],s[Et]=s.maxHeight=n[Et],s[Bt]=n[Bt],e.parentNode.insertBefore(t,e),t.appendChild(e)}}function te(e){for(var t=$t.length,n=e.style,r=[],i=0;i<t;i++)r.push($t[i],n[$t[i]]);return r.t=e,r}function ne(e,t,n,r,i,u,o,s,a,c,D,f,d){A(e)&&(e=e(s)),B(e)&&"max"===e.substr(0,3)&&(e=f+("="===e.charAt(4)?W("0"+e.substr(3),n):0));var p,h,g,m=d?d.time():0;if(d&&d.seek(0),_(e))o&&Lt(o,n,r,!0);else{A(t)&&(t=t(s));var v,C,y,F,E=e.split(" ");g=l(t)||Ie,(v=Mt(g)||{})&&(v.left||v.top)||"none"!==N(g).display||(F=g.style.display,g.style.display="block",v=Mt(g),F?g.style.display=F:g.style.removeProperty("display")),C=W(E[0],v[r.d]),y=W(E[1]||"0",n),e=v[r.p]-a[r.p]-c+C+i-y,o&&Lt(o,y,r,n-y<20||o._isStart&&20<y),n-=n-y}if(u){var x=e+n,b=u._isStart;p="scroll"+r.d2,Lt(u,x,r,b&&20<x||!b&&(D?Math.max(Ie[p],Re[p]):u.parentNode[p])<=x+1),D&&(a=Mt(o),D&&(u.style[r.op.p]=a[r.op.p]-r.op.m-u._offset+kt))}return d&&g&&(p=Mt(g),d.seek(f),h=Mt(g),d._caScrollDist=p[r.p]-h[r.p],e=e/d._caScrollDist*f),d&&d.seek(m),d?e:Math.round(e)}function re(e,t,n,r){if(e.parentNode!==t){var i,u,o=e.style;if(t===Ie){for(i in e._stOrig=o.cssText,u=N(e))+i||tn.test(i)||!u[i]||"string"!=typeof o[i]||"0"===i||(o[i]=u[i]);o.top=n,o.left=r}else o.cssText=e._stOrig;Oe.core.getCache(e).uncache=1,t.appendChild(e)}}function ie(e,t){function n(t,s,a,l,c){var D=n.tween,f=s.onComplete,d={};return a=a||u(),c=l&&c||0,l=l||t-a,D&&D.kill(),r=Math.round(a),s[o]=t,(s.modifiers=d)[o]=function(e){return(e=y(u()))!==r&&e!==i&&2<Math.abs(e-r)&&2<Math.abs(e-i)?(D.kill(),n.tween=0):e=a+l*D.ratio+c*D.ratio*D.ratio,i=r,r=y(e)},s.onComplete=function(){n.tween=0,f&&f.call(D)},D=n.tween=Oe.to(e,s)}var r,i,u=c(e,t),o="_scroll"+t.p2;return(e[o]=u).wheelHandler=function(){return n.tween&&n.tween.kill()&&(n.tween=0)},X(e,"wheel",u.wheelHandler),n}function ue(e,t){Ne||ue.register(Oe)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),this.init(e,t)}function oe(e,t,n,r){return r<t?e(r):t<0&&e(0),r<n?(r-t)/(n-t):n<0?t/(t-n):1}function se(e,t){!0===t?e.style.removeProperty("touch-action"):e.style.touchAction=!0===t?"auto":t?"pan-"+t+(Pe.isTouch?" pinch-zoom":""):"none",e===Re&&se(Ie,t)}function ae(e){var t,n=e.event,r=e.target,i=e.axis,u=(n.changedTouches?n.changedTouches[0]:n).target,o=u._gsap||Oe.core.getCache(u),s=pt();if(!o._isScrollT||2e3<s-o._isScrollT){for(;u&&u.scrollHeight<=u.clientHeight;)u=u.parentNode;o._isScroll=u&&!x(u)&&u!==r&&(un[(t=N(u)).overflowY]||un[t.overflowX]),o._isScrollT=s}!o._isScroll&&"x"!==i||(n._gsapAllow=!0)}function le(e,t,n,r){return Pe.create({target:e,capture:!0,debounce:!1,lockAxis:!0,type:t,onWheel:r=r&&ae,onPress:r,onDrag:r,onScroll:r,onEnable:function e(){return n&&X(Ve,Pe.eventTypes[0],sn,!1,!0)},onDisable:function e(){return z(Ve,Pe.eventTypes[0],sn,!0)}})}function ce(e){function t(){return o=!1}function n(){u=w(m,Me),P=He(ot?1:0,u),p&&(M=He(0,w(m,ke))),s=qt}function r(){n(),a.isActive()&&a.vars.scrollY>u&&(E()>u?a.progress(1)&&E(u):a.resetTo("scrollY",u))}T(e)||(e={}),e.preventDefault=e.isNormalizer=e.allowClicks=!0,e.type||(e.type="wheel,touch"),e.debounce=!!e.debounce,e.id=e.id||"normalizer";var i,u,o,s,a,D,f,d,p=e.normalizeScrollX,h=e.momentum,g=e.allowNestedScroll,m=l(e.target)||Re,v=Oe.core.globals().ScrollSmoother,F=ot&&(e.content&&l(e.content)||v&&v.get()&&v.get().content()),E=c(m,Me),x=c(m,ke),b=1,S=(Pe.isTouch&&Le.visualViewport?Le.visualViewport.scale*Le.visualViewport.width:Le.outerWidth)/Le.innerWidth,B=0,_=A(h)?function(){return h(i)}:function(){return h||2.8},k=le(m,e.type,!0,g),M=C,P=C;return e.ignoreCheck=function(e){return ot&&"touchmove"===e.type&&function e(){if(o){requestAnimationFrame(t);var n=y(i.deltaY/2),r=P(E.v-n);return F&&r!==E.v+E.offset&&(E.offset=r-E.v,F.style.transform="translateY("+-E.offset+"px)",F._gsap&&(F._gsap.y=-E.offset+"px"),E.cacheID=we.cache,Kt()),!0}F&&(F.style.transform="translateY(0px)",E.offset=E.cacheID=0,F._gsap&&(F._gsap.y="0px")),o=!0}()||1.05<b&&"touchstart"!==e.type||i.isGesturing||e.touches&&1<e.touches.length},e.onPress=function(){var e=b;b=y((Le.visualViewport&&Le.visualViewport.scale||1)/S),a.pause(),e!==b&&se(m,1.01<b||!p&&"x"),o=!1,D=x(),f=E(),n(),s=qt},e.onRelease=e.onGestureStart=function(e,t){if(F&&(F.style.transform="translateY(0px)",E.offset=E.cacheID=0,F._gsap&&(F._gsap.y="0px")),t){we.cache++;var n,i,o=_();p&&(i=(n=x())+.05*o*-e.velocityX/.227,o*=oe(x,n,i,w(m,ke)),a.vars.scrollX=M(i)),i=(n=E())+.05*o*-e.velocityY/.227,o*=oe(E,n,i,w(m,Me)),a.vars.scrollY=P(i),a.invalidate().duration(o).play(.01),(ot&&a.vars.scrollY>=u||u-1<=n)&&Oe.to({},{onUpdate:r,duration:o})}else d.restart(!0)},e.onWheel=function(){a._ts&&a.pause(),1e3<pt()-B&&(s=0,B=pt())},e.onChange=function(e,t,r,i,u){qt!==s&&n(),t&&p&&x(M(i[2]===t?D+(e.startX-e.x):x()+t-i[1])),r&&E(P(u[2]===r?f+(e.startY-e.y):E()+r-u[1])),Kt()},e.onEnable=function(){se(m,!p&&"x"),X(Le,"resize",r),k.enable()},e.onDisable=function(){se(m,!0),z(Le,"resize",r),k.kill()},((i=new Pe(e)).iOS=ot)&&!E()&&E(1),d=i._dc,a=Oe.to(i,{ease:"power4",paused:!0,scrollX:p?"+=0.1":"+=0",scrollY:"+=0.1",onComplete:d.vars.onComplete}),i}var De,fe,de,pe,he,ge,me,ve,Ce,ye,Fe,Ee,xe=1,be=[],we=[],Se=[],Be=Date.now,Ae=function e(t,n){return n},_e="scrollLeft",Te="scrollTop",ke={s:_e,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:a((function(e){return arguments.length?de.scrollTo(e,Me.sc()):de.pageXOffset||pe[_e]||he[_e]||ge[_e]||0}))},Me={s:Te,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:ke,sc:a((function(e){return arguments.length?de.scrollTo(ke.sc(),e):de.pageYOffset||pe[Te]||he[Te]||ge[Te]||0}))};ke.op=Me,we.cache=0;var Pe=(g.prototype.init=function e(t){function n(){return Ze=Be()}function r(e,t){return(Re.event=e)&&V&&~V.indexOf(e.target)||t&&je&&"touch"!==e.pointerType||ie&&ie(e,t)}function a(){var e=Re.deltaX=d(Ge),t=Re.deltaY=d(Ke),n=Math.abs(e)>=A,r=Math.abs(t)>=A;J&&(n||r)&&J(Re,e,t,Ge,Ke),n&&(j&&0<Re.deltaX&&j(Re),q&&Re.deltaX<0&&q(Re),K&&K(Re),Q&&Re.deltaX<0!=Ie<0&&Q(Re),Ie=Re.deltaX,Ge[0]=Ge[1]=Ge[2]=0),r&&(G&&0<Re.deltaY&&G(Re),U&&Re.deltaY<0&&U(Re),Z&&Z(Re),ee&&Re.deltaY<0!=Ye<0&&ee(Re),Ye=Re.deltaY,Ke[0]=Ke[1]=Ke[2]=0),(Oe||Pe)&&(re&&re(Re),$&&Le&&$(Re),Pe&&(z(Re),Pe=!1),Oe=Le=!1),Ne&&(ae(Re),Ne=!1),_e=0}function g(e,t,n){Ge[n]+=e,Ke[n]+=t,Re._vx.update(e),Re._vy.update(t),P?_e=_e||requestAnimationFrame(a):a()}function m(e,t){"y"!==Ve&&(Ge[2]+=e,Re._vx.update(e,!0)),"x"!==Ve&&(Ke[2]+=t,Re._vy.update(t,!0)),Ae&&!Ve&&(Re.axis=Ve=Math.abs(e)>Math.abs(t)?"x":"y",Le=!0),P?_e=_e||requestAnimationFrame(a):a()}function v(e){if(!r(e,1)){var t=(e=f(e,O)).clientX,n=e.clientY,i=t-Re.x,u=n-Re.y,o=Re.isDragging;Re.x=t,Re.y=n,(o||Math.abs(Re.startX-t)>=_||Math.abs(Re.startY-n)>=_)&&(z&&(Pe=!0),o||(Re.isDragging=!0),m(i,u),o||Y&&Y(Re))}}function C(e){if(!r(e,1)){o(ue?k:Ue,Ee[1],v,!0);var t=Re.isDragging&&(3<Math.abs(Re.x-Re.startX)||3<Math.abs(Re.y-Re.startY)),n=f(e);t||(Re._vx.reset(),Re._vy.reset(),O&&Se&&De.delayedCall(.08,(function(){if(300<Be()-Ze&&!e.defaultPrevented)if(e.target.click)e.target.click();else if(Ue.createEvent){var t=Ue.createEvent("MouseEvents");t.initMouseEvent("click",!0,!0,de,1,n.screenX,n.screenY,n.clientX,n.clientY,!1,!1,!1,!1,0,null),e.target.dispatchEvent(t)}}))),Re.isDragging=Re.isGesturing=Re.isPressed=!1,N&&!ue&&Te.restart(!0),X&&t&&X(Re),W&&W(Re,t)}}function y(e){return e.touches&&1<e.touches.length&&(Re.isGesturing=!0)&&oe(e,Re.isDragging)}function F(){return(Re.isGesturing=!1)||se(Re)}function E(e){if(!r(e)){var t=Xe(),n=ze();g((t-He)*xe,(n-We)*xe,1),He=t,We=n,N&&Te.restart(!0)}}function x(e){if(!r(e)){e=f(e,O),ae&&(Ne=!0);var t=(1===e.deltaMode?M:2===e.deltaMode?de.innerHeight:1)*R;g(e.deltaX*t,e.deltaY*t,0),N&&!ue&&Te.restart(!0)}}function b(e){if(!r(e)){var t=e.clientX,n=e.clientY,i=t-Re.x,u=n-Re.y;Re.x=t,Re.y=n,Oe=!0,(i||u)&&m(i,u)}}function w(e){Re.event=e,te(Re)}function S(e){Re.event=e,ne(Re)}function B(e){return r(e)||f(e,O)&&ye(Re)}fe||h(De)||console.warn("Please gsap.registerPlugin(Observer)"),Ce||p();var A=t.tolerance,_=t.dragMinimum,T=t.type,k=t.target,M=t.lineHeight,P=t.debounce,O=t.preventDefault,N=t.onStop,L=t.onStopDelay,V=t.ignore,R=t.wheelSpeed,I=t.event,Y=t.onDragStart,X=t.onDragEnd,z=t.onDrag,H=t.onPress,W=t.onRelease,j=t.onRight,q=t.onLeft,U=t.onUp,G=t.onDown,K=t.onChangeX,Z=t.onChangeY,J=t.onChange,Q=t.onToggleX,ee=t.onToggleY,te=t.onHover,ne=t.onHoverEnd,re=t.onMove,ie=t.ignoreCheck,ue=t.isNormalizer,oe=t.onGestureStart,se=t.onGestureEnd,ae=t.onWheel,le=t.onEnable,ce=t.onDisable,ye=t.onClick,xe=t.scrollSpeed,we=t.capture,Se=t.allowClicks,Ae=t.lockAxis,$=t.onLockAxis;this.target=k=l(k)||he,this.vars=t,V=V&&De.utils.toArray(V),A=A||0,_=_||0,R=R||1,xe=xe||1,T=T||"wheel,touch,pointer",P=!1!==P,M=M||parseFloat(de.getComputedStyle(ge).lineHeight)||22;var _e,Te,Pe,Oe,Ne,Le,Ve,Re=this,Ie=0,Ye=0,Xe=c(k,ke),ze=c(k,Me),He=Xe(),We=ze(),je=~T.indexOf("touch")&&!~T.indexOf("pointer")&&"pointerdown"===Ee[0],qe=i(k),Ue=k.ownerDocument||pe,Ge=[0,0,0],Ke=[0,0,0],Ze=0,$e=Re.onPress=function(e){r(e,1)||(Re.axis=Ve=null,Te.pause(),Re.isPressed=!0,e=f(e),Ie=Ye=0,Re.startX=Re.x=e.clientX,Re.startY=Re.y=e.clientY,Re._vx.reset(),Re._vy.reset(),u(ue?k:Ue,Ee[1],v,O,!0),Re.deltaX=Re.deltaY=0,H&&H(Re))};Te=Re._dc=De.delayedCall(L||.25,(function e(){Re._vx.reset(),Re._vy.reset(),Te.pause(),N&&N(Re)})).pause(),Re.deltaX=Re.deltaY=0,Re._vx=D(0,50,!0),Re._vy=D(0,50,!0),Re.scrollX=Xe,Re.scrollY=ze,Re.isDragging=Re.isGesturing=Re.isPressed=!1,Re.enable=function(e){return Re.isEnabled||(u(qe?Ue:k,"scroll",s),0<=T.indexOf("scroll")&&u(qe?Ue:k,"scroll",E,O,we),0<=T.indexOf("wheel")&&u(k,"wheel",x,O,we),(0<=T.indexOf("touch")&&me||0<=T.indexOf("pointer"))&&(u(k,Ee[0],$e,O,we),u(Ue,Ee[2],C),u(Ue,Ee[3],C),Se&&u(k,"click",n,!1,!0),ye&&u(k,"click",B),oe&&u(Ue,"gesturestart",y),se&&u(Ue,"gestureend",F),te&&u(k,ve+"enter",w),ne&&u(k,ve+"leave",S),re&&u(k,ve+"move",b)),Re.isEnabled=!0,e&&e.type&&$e(e),le&&le(Re)),Re},Re.disable=function(){Re.isEnabled&&(be.filter((function(e){return e!==Re&&i(e.target)})).length||o(qe?Ue:k,"scroll",s),Re.isPressed&&(Re._vx.reset(),Re._vy.reset(),o(ue?k:Ue,Ee[1],v,!0)),o(qe?Ue:k,"scroll",E,we),o(k,"wheel",x,we),o(k,Ee[0],$e,we),o(Ue,Ee[2],C),o(Ue,Ee[3],C),o(k,"click",n,!0),o(k,"click",B),o(Ue,"gesturestart",y),o(Ue,"gestureend",F),o(k,ve+"enter",w),o(k,ve+"leave",S),o(k,ve+"move",b),Re.isEnabled=Re.isPressed=Re.isDragging=!1,ce&&ce(Re))},Re.kill=function(){Re.disable();var e=be.indexOf(Re);0<=e&&be.splice(e,1),Fe===Re&&(Fe=0)},be.push(Re),ue&&i(k)&&(Fe=Re),Re.enable(I)},function e(n,r,i){r&&t(n.prototype,r),i&&t(n,i)}(g,[{key:"velocityX",get:function e(){return this._vx.getVelocity()}},{key:"velocityY",get:function e(){return this._vy.getVelocity()}}]),g);Pe.version="3.10.4",Pe.create=function(e){return new Pe(e)},Pe.register=h,Pe.getAll=function(){return be.slice()},Pe.getById=function(e){return be.filter((function(t){return t.vars.id===e}))[0]},n()&&De.registerPlugin(Pe);var Oe,Ne,Le,Ve,Re,Ie,Ye,Xe,ze,He,We,je,qe,Ue,Ge,Ke,Ze,$e,Je,Qe,et,tt,nt,rt,it,ut,ot,st,at,lt,ct,Dt,ft,dt=1,pt=Date.now,ht=pt(),gt=0,mt=0,vt=Math.abs,Ct="right",yt="bottom",Ft="width",Et="height",xt="Right",bt="Left",wt="Top",St="Bottom",Bt="padding",At="margin",_t="Width",Tt="Height",kt="px",Mt=function e(t,n){var r=n&&"matrix(1, 0, 0, 1, 0, 0)"!==N(t)[Ge]&&Oe.to(t,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),i=t.getBoundingClientRect();return r&&r.progress(0).kill(),i},Pt={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},Ot={toggleActions:"play",anticipatePin:0},Nt={top:0,left:0,center:.5,bottom:1,right:1},Lt=function e(t,n,r,i){var u={display:"block"},o=r[i?"os2":"p2"],s=r[i?"p2":"os2"];t._isFlipped=i,u[r.a+"Percent"]=i?-100:0,u[r.a]=i?"1px":0,u["border"+o+_t]=1,u["border"+s+_t]=0,u[r.p]=n+"px",Oe.set(t,u)},Vt=[],Rt={},It={},Yt=[],Xt=[],zt=function e(t){return It[t]&&It[t].map((function(e){return e()}))||Yt},Ht=[],Wt=function e(t){for(var n=0;n<Ht.length;n+=5)t&&Ht[n+4]!==t||(Ht[n].style.cssText=Ht[n+1],Ht[n].getBBox&&Ht[n].setAttribute("transform",Ht[n+2]||""),Ht[n+3].uncache=1)},jt=function e(t,n){var r;for(Ke=0;Ke<Vt.length;Ke++)r=Vt[Ke],n&&r.media!==n||(t?r.kill(1):r.revert());n&&Wt(n),n||zt("revert")},qt=0,Ut=function e(t,n){if(!gt||t){Dt=!0;var r=zt("refreshInit");Qe&&nn.sort(),n||jt(),Vt.slice(0).forEach((function(e){return e.refresh()})),Vt.forEach((function(e){return"max"===e.vars.end&&e.setPositions(e.start,w(e.scroller,e._dir))})),r.forEach((function(e){return e&&e.render&&e.render(-1)})),Q(),Xe.pause(),qt++,Dt=!1,zt("refresh")}else X(nn,"scrollEnd",J)},$=0,Gt=1,Kt=function e(){if(!Dt){nn.isUpdating=!0,ft&&ft.update(0);var t=Vt.length,n=pt(),r=50<=n-ht,i=t&&Vt[0].scroll();if(Gt=i<$?-1:1,$=i,r&&(gt&&!Ue&&200<n-gt&&(gt=0,zt("scrollEnd")),We=ht,ht=n),Gt<0){for(Ke=t;0<Ke--;)Vt[Ke]&&Vt[Ke].update(0,r);Gt=1}else for(Ke=0;Ke<t;Ke++)Vt[Ke]&&Vt[Ke].update(0,r);nn.isUpdating=!1}at=0},Zt=["left","top",yt,Ct,At+St,At+xt,At+wt,At+bt,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],$t=Zt.concat([Ft,Et,"boxSizing","max"+_t,"max"+Tt,"position",At,Bt,Bt+wt,Bt+xt,Bt+St,Bt+bt]),Jt=/([A-Z])/g,Qt=function e(t){if(t){var n,r,i=t.t.style,u=t.length,o=0;for((t.t._gsap||Oe.core.getCache(t.t)).uncache=1;o<u;o+=2)r=t[o+1],n=t[o],r?i[n]=r:i[n]&&i.removeProperty(n.replace(Jt,"-$1").toLowerCase())}},en={left:0,top:0},tn=/(webkit|moz|length|cssText|inset)/i,nn=(ue.prototype.init=function e(t,n){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),mt){var i,u,o,s,a,D,f,d,p,h,g,m,v,F,E,S,k,M,Y,H,q,G,Z,Q,oe,se,ae,le,ce,De,fe,de,pe,he,ge,me,ve,Ce,ye=(t=L(B(t)||_(t)||t.nodeType?{trigger:t}:t,Ot)).onUpdate,$=t.toggleClass,Fe=t.id,Ee=t.onToggle,xe=t.onRefresh,be=t.scrub,we=t.trigger,Be=t.pin,Ae=t.pinSpacing,_e=t.invalidateOnRefresh,Te=t.anticipatePin,Pe=t.onScrubComplete,Ne=t.onSnapComplete,Ye=t.once,Xe=t.snap,je=t.pinReparent,Ge=t.pinSpacer,Ze=t.containerAnimation,$e=t.fastScrollEnd,Je=t.preventOverlaps,tt=t.horizontal||t.containerAnimation&&!1!==t.horizontal?ke:Me,nt=!be&&0!==be,rt=l(t.scroller||Le),it=Oe.core.getCache(rt),ut=x(rt),ot="fixed"===("pinType"in t?t.pinType:r(rt,"pinType")||ut&&"fixed"),at=[t.onEnter,t.onLeave,t.onEnterBack,t.onLeaveBack],ct=nt&&t.toggleActions.split(" "),ht="markers"in t?t.markers:Ot.markers,Ct=ut?0:parseFloat(N(rt)["border"+tt.p2+_t])||0,yt=this,Tt=t.onRefreshInit&&function(){return t.onRefreshInit(yt)},Nt=function e(t,n,i){var u=i.d,o=i.d2,s=i.a;return(s=r(t,"getBoundingClientRect"))?function(){return s()[u]}:function(){return(n?Le["inner"+o]:t["client"+o])||0}}(rt,ut,tt),Lt=function e(t,n){return!n||~Se.indexOf(t)?b(t):function(){return en}}(rt,ut),It=0,Yt=0,Xt=c(rt,tt);if(yt.media=lt,yt._dir=tt,Te*=45,yt.scroller=rt,yt.scroll=Ze?Ze.time.bind(Ze):Xt,s=Xt(),yt.vars=t,n=n||t.animation,"refreshPriority"in t&&(Qe=1,-9999===t.refreshPriority&&(ft=yt)),it.tweenScroll=it.tweenScroll||{top:ie(rt,Me),left:ie(rt,ke)},yt.tweenTo=i=it.tweenScroll[tt.p],yt.scrubDuration=function(e){(fe=_(e)&&e)?De?De.duration(e):De=Oe.to(n,{ease:"expo",totalProgress:"+=0.001",duration:fe,paused:!0,onComplete:function e(){return Pe&&Pe(yt)}}):(De&&De.progress(1).kill(),De=0)},n&&(n.vars.lazy=!1,n._initted||!1!==n.vars.immediateRender&&!1!==t.immediateRender&&n.render(0,!0,!0),yt.animation=n.pause(),(n.scrollTrigger=yt).scrubDuration(be),le=0,Fe=Fe||n.vars.id),Vt.push(yt),Xe&&(T(Xe)&&!Xe.push||(Xe={snapTo:Xe}),"scrollBehavior"in Ie.style&&Oe.set(ut?[Ie,Re]:rt,{scrollBehavior:"auto"}),o=A(Xe.snapTo)?Xe.snapTo:"labels"===Xe.snapTo?function e(t){return function(e){return Oe.utils.snap(R(t),e)}}(n):"labelsDirectional"===Xe.snapTo?function e(t){return function(e,n){return I(R(t))(e,n.direction)}}(n):!1!==Xe.directional?function(e,t){return I(Xe.snapTo)(e,pt()-Yt<500?0:t.direction)}:Oe.utils.snap(Xe.snapTo),de=T(de=Xe.duration||{min:.1,max:2})?He(de.min,de.max):He(de,de),pe=Oe.delayedCall(Xe.delay||fe/2||.1,(function(){var e=Xt(),t=pt()-Yt<500,r=i.tween;if(!(t||Math.abs(yt.getVelocity())<10)||r||Ue||It===e)yt.isActive&&It!==e&&pe.restart(!0);else{var u=(e-D)/v,s=n&&!nt?n.totalProgress():u,a=t?0:(s-ce)/(pt()-We)*1e3||0,l=Oe.utils.clamp(-u,1-u,vt(a/2)*a/.185),c=u+(!1===Xe.inertia?0:l),d=He(0,1,o(c,yt)),p=Math.round(D+d*v),h=Xe.onStart,g=Xe.onInterrupt,m=Xe.onComplete;if(e<=f&&D<=e&&p!==e){if(r&&!r._initted&&r.data<=vt(p-e))return;!1===Xe.inertia&&(l=d-u),i(p,{duration:de(vt(.185*Math.max(vt(c-s),vt(d-s))/a/.05||0)),ease:Xe.ease||"power3",data:vt(p-e),onInterrupt:function e(){return pe.restart(!0)&&g&&g(yt)},onComplete:function e(){yt.update(),It=Xt(),le=ce=n&&!nt?n.totalProgress():yt.progress,Ne&&Ne(yt),m&&m(yt)}},e,l*v,p-e-l*v),h&&h(yt,i.tween)}}})).pause()),Fe&&(Rt[Fe]=yt),Ce=(Ce=(we=yt.trigger=l(we||Be))&&we._gsap&&we._gsap.stRevert)&&Ce(yt),Be=!0===Be?we:l(Be),B($)&&($={targets:we,className:$}),Be&&(!1===Ae||Ae===At||(Ae=!(!Ae&&"flex"===N(Be.parentNode).display)&&Bt),yt.pin=Be,!1!==t.force3D&&Oe.set(Be,{force3D:!0}),(u=Oe.core.getCache(Be)).spacer?F=u.pinState:(Ge&&((Ge=l(Ge))&&!Ge.nodeType&&(Ge=Ge.current||Ge.nativeElement),u.spacerIsNative=!!Ge,Ge&&(u.spacerState=te(Ge))),u.spacer=k=Ge||Ve.createElement("div"),k.classList.add("pin-spacer"),Fe&&k.classList.add("pin-spacer-"+Fe),u.pinState=F=te(Be)),yt.spacer=k=u.spacer,ae=N(Be),Z=ae[Ae+tt.os2],Y=Oe.getProperty(Be),H=Oe.quickSetter(Be,tt.a,kt),ee(Be,k,ae),S=te(Be)),ht){m=T(ht)?L(ht,Pt):Pt,h=j("scroller-start",Fe,rt,tt,m,0),g=j("scroller-end",Fe,rt,tt,m,0,h),M=h["offset"+tt.op.d2];var zt=l(r(rt,"content")||rt);d=this.markerStart=j("start",Fe,zt,tt,m,M,0,Ze),p=this.markerEnd=j("end",Fe,zt,tt,m,M,0,Ze),Ze&&(ve=Oe.quickSetter([d,p],tt.a,kt)),ot||Se.length&&!0===r(rt,"fixedMarkers")||(function e(t){var n=N(t).position;t.style.position="absolute"===n||"fixed"===n?n:"relative"}(ut?Ie:rt),Oe.set([h,g],{force3D:!0}),oe=Oe.quickSetter(h,tt.a,kt),se=Oe.quickSetter(g,tt.a,kt))}if(Ze){var Ht=Ze.vars.onUpdate,Wt=Ze.vars.onUpdateParams;Ze.eventCallback("onUpdate",(function(){yt.update(0,0,1),Ht&&Ht.apply(Wt||[])}))}yt.previous=function(){return Vt[Vt.indexOf(yt)-1]},yt.next=function(){return Vt[Vt.indexOf(yt)+1]},yt.revert=function(e){var t=!1!==e||!yt.enabled,r=qe;t!==yt.isReverted&&(t&&(!yt.scroll.rec&&qe&&Dt&&(yt.scroll.rec=Xt()),ge=Math.max(Xt(),yt.scroll.rec||0),he=yt.progress,me=n&&n.progress()),d&&[d,p,h,g].forEach((function(e){return e.style.display=t?"none":"block"})),t&&(qe=1),yt.update(t),qe=r,Be&&(t?function e(t,n,r){Qt(r);var i=t._gsap;if(i.spacerIsNative)Qt(i.spacerState);else if(t.parentNode===n){var u=n.parentNode;u&&(u.insertBefore(t,n),u.removeChild(n))}}(Be,k,F):je&&yt.isActive||ee(Be,k,N(Be),Q)),yt.isReverted=t)},yt.refresh=function(e,r){if(!qe&&yt.enabled||r)if(Be&&e&&gt)X(ue,"scrollEnd",J);else{!Dt&&Tt&&Tt(yt),qe=1,Yt=pt(),i.tween&&(i.tween.kill(),i.tween=0),De&&De.pause(),_e&&n&&n.time(-.01,!0).invalidate(),yt.isReverted||yt.revert();for(var u,o,m,C,y,x,b,T,M,P,O=Nt(),L=Lt(),R=Ze?Ze.duration():w(rt,tt),I=0,z=0,H=t.end,j=t.endTrigger||we,U=t.start||(0!==t.start&&we?Be?"0 0":"0 100%":0),K=yt.pinnedContainer=t.pinnedContainer&&l(t.pinnedContainer),Z=we&&Math.max(0,Vt.indexOf(yt))||0,re=Z;re--;)(x=Vt[re]).end||x.refresh(0,1)||(qe=1),!(b=x.pin)||b!==we&&b!==Be||x.isReverted||((P=P||[]).unshift(x),x.revert()),x!==Vt[re]&&(Z--,re--);for(A(U)&&(U=U(yt)),D=ne(U,we,O,tt,Xt(),d,h,yt,L,Ct,ot,R,Ze)||(Be?-.001:0),A(H)&&(H=H(yt)),B(H)&&!H.indexOf("+=")&&(~H.indexOf(" ")?H=(B(U)?U.split(" ")[0]:"")+H:(I=W(H.substr(2),O),H=B(U)?U:D+I,j=we)),f=Math.max(D,ne(H||(j?"100% 0":R),j,O,tt,Xt()+I,p,g,yt,L,Ct,ot,R,Ze))||-.001,v=f-D||(D-=.01)&&.001,I=0,re=Z;re--;)(b=(x=Vt[re]).pin)&&x.start-x._pinPush<D&&!Ze&&0<x.end&&(u=x.end-x.start,b!==we&&b!==K||_(U)||(I+=u*(1-x.progress)),b===Be&&(z+=u));if(D+=I,f+=I,yt._pinPush=z,d&&I&&((u={})[tt.a]="+="+I,K&&(u[tt.p]="-="+Xt()),Oe.set([d,p],u)),Be)u=N(Be),C=tt===Me,m=Xt(),q=parseFloat(Y(tt.a))+z,!R&&1<f&&((ut?Ie:rt).style["overflow-"+tt.a]="scroll"),ee(Be,k,u),S=te(Be),o=Mt(Be,!0),T=ot&&c(rt,C?ke:Me)(),Ae&&((Q=[Ae+tt.os2,v+z+kt]).t=k,(re=Ae===Bt?V(Be,tt)+v+z:0)&&Q.push(tt.d,re+kt),Qt(Q),ot&&Xt(ge)),ot&&((y={top:o.top+(C?m-D:T)+kt,left:o.left+(C?T:m-D)+kt,boxSizing:"border-box",position:"fixed"})[Ft]=y.maxWidth=Math.ceil(o.width)+kt,y[Et]=y.maxHeight=Math.ceil(o.height)+kt,y[At]=y[At+wt]=y[At+xt]=y[At+St]=y[At+bt]="0",y[Bt]=u[Bt],y[Bt+wt]=u[Bt+wt],y[Bt+xt]=u[Bt+xt],y[Bt+St]=u[Bt+St],y[Bt+bt]=u[Bt+bt],E=function e(t,n,r){for(var i,u=[],o=t.length,s=r?8:0;s<o;s+=2)i=t[s],u.push(i,i in n?n[i]:t[s+1]);return u.t=t.t,u}(F,y,je)),n?(M=n._initted,et(1),n.render(n.duration(),!0,!0),G=Y(tt.a)-q+v+z,v!==G&&ot&&E.splice(E.length-2,2),n.render(0,!0,!0),M||n.invalidate(),et(0)):G=v;else if(we&&Xt()&&!Ze)for(o=we.parentNode;o&&o!==Ie;)o._pinOffset&&(D-=o._pinOffset,f-=o._pinOffset),o=o.parentNode;P&&P.forEach((function(e){return e.revert(!1)})),yt.start=D,yt.end=f,s=a=Xt(),Ze||(s<ge&&Xt(ge),yt.scroll.rec=0),yt.revert(!1),pe&&(It=-1,yt.isActive&&Xt(D+v*he),pe.restart(!0)),qe=0,n&&nt&&(n._initted||me)&&n.progress()!==me&&n.progress(me,!0).render(n.time(),!0,!0),he===yt.progress&&!Ze||(n&&!nt&&n.totalProgress(he,!0),yt.progress=he,yt.update(0,0,1)),Be&&Ae&&(k._pinOffset=Math.round(yt.progress*G)),xe&&xe(yt)}},yt.getVelocity=function(){return(Xt()-a)/(pt()-We)*1e3||0},yt.endAnimation=function(){P(yt.callbackAnimation),n&&(De?De.progress(1):n.paused()?nt||P(n,yt.direction<0,1):P(n,n.reversed()))},yt.labelToScroll=function(e){return n&&n.labels&&(D||yt.refresh()||D)+n.labels[e]/n.duration()*v||0},yt.getTrailing=function(e){var t=Vt.indexOf(yt),n=0<yt.direction?Vt.slice(0,t).reverse():Vt.slice(t+1);return(B(e)?n.filter((function(t){return t.vars.preventOverlaps===e})):n).filter((function(e){return 0<yt.direction?e.end<=D:e.start>=f}))},yt.update=function(e,t,r){if(!Ze||r||e){var u,o,l,c,d,p,g,m=yt.scroll(),C=e?0:(m-D)/v,F=C<0?0:1<C?1:C||0,x=yt.progress;if(t&&(a=s,s=Ze?Xt():m,Xe&&(ce=le,le=n&&!nt?n.totalProgress():F)),Te&&!F&&Be&&!qe&&!dt&&gt&&D<m+(m-a)/(pt()-We)*Te&&(F=1e-4),F!==x&&yt.enabled){if(c=(d=(u=yt.isActive=!!F&&F<1)!=(!!x&&x<1))||!!F!=!!x,yt.direction=x<F?1:-1,yt.progress=F,c&&!qe&&(o=F&&!x?0:1===F?1:1===x?2:3,nt&&(l=!d&&"none"!==ct[o+1]&&ct[o+1]||ct[o],g=n&&("complete"===l||"reset"===l||l in n))),Je&&(d||g)&&(g||be||!n)&&(A(Je)?Je(yt):yt.getTrailing(Je).forEach((function(e){return e.endAnimation()}))),nt||(!De||qe||dt?n&&n.totalProgress(F,!!qe):((Ze||ft&&ft!==yt)&&De.render(De._dp._time-De._start),De.resetTo?De.resetTo("totalProgress",F,n._tTime/n._tDur):(De.vars.totalProgress=F,De.invalidate().restart()))),Be)if(e&&Ae&&(k.style[Ae+tt.os2]=Z),ot){if(c){if(p=!e&&x<F&&m<f+1&&m+1>=w(rt,tt),je)if(e||!u&&!p)re(Be,k);else{var b=Mt(Be,!0),B=m-D;re(Be,Ie,b.top+(tt===Me?B:0)+kt,b.left+(tt===Me?0:B)+kt)}Qt(u||p?E:S),G!==v&&F<1&&u||H(q+(1!==F||p?0:G))}}else H(y(q+G*F));!Xe||i.tween||qe||dt||pe.restart(!0),$&&(d||Ye&&F&&(F<1||!st))&&ze($.targets).forEach((function(e){return e.classList[u||Ye?"add":"remove"]($.className)})),!ye||nt||e||ye(yt),c&&!qe?(nt&&(g&&("complete"===l?n.pause().totalProgress(1):"reset"===l?n.restart(!0).pause():"restart"===l?n.restart(!0):n[l]()),ye&&ye(yt)),!d&&st||(Ee&&d&&O(yt,Ee),at[o]&&O(yt,at[o]),Ye&&(1===F?yt.kill(!1,1):at[o]=0),d||at[o=1===F?1:3]&&O(yt,at[o])),$e&&!u&&Math.abs(yt.getVelocity())>(_($e)?$e:2500)&&(P(yt.callbackAnimation),De?De.progress(1):P(n,!F,1))):nt&&ye&&!qe&&ye(yt)}if(se){var T=Ze?m/Ze.duration()*(Ze._caScrollDist||0):m;oe(T+(h._isFlipped?1:0)),se(T)}ve&&ve(-m/Ze.duration()*(Ze._caScrollDist||0))}},yt.enable=function(e,t){yt.enabled||(yt.enabled=!0,X(rt,"resize",K),X(ut?Ve:rt,"scroll",U),Tt&&X(ue,"refreshInit",Tt),!1!==e&&(yt.progress=he=0,s=a=It=Xt()),!1!==t&&yt.refresh())},yt.getTween=function(e){return e&&i?i.tween:De},yt.setPositions=function(e,t){Be&&(q+=e-D,G+=t-e-v),yt.start=D=e,yt.end=f=t,v=t-e,yt.update()},yt.disable=function(e,t){if(yt.enabled&&(!1!==e&&yt.revert(),yt.enabled=yt.isActive=!1,t||De&&De.pause(),ge=0,u&&(u.uncache=1),Tt&&z(ue,"refreshInit",Tt),pe&&(pe.pause(),i.tween&&i.tween.kill()&&(i.tween=0)),!ut)){for(var n=Vt.length;n--;)if(Vt[n].scroller===rt&&Vt[n]!==yt)return;z(rt,"resize",K),z(rt,"scroll",U)}},yt.kill=function(e,r){yt.disable(e,r),De&&!r&&De.kill(),Fe&&delete Rt[Fe];var i=Vt.indexOf(yt);0<=i&&Vt.splice(i,1),i===Ke&&0<Gt&&Ke--,i=0,Vt.forEach((function(e){return e.scroller===yt.scroller&&(i=1)})),i||(yt.scroll.rec=0),n&&(n.scrollTrigger=null,e&&n.render(-1),r||n.kill()),d&&[d,p,h,g].forEach((function(e){return e.parentNode&&e.parentNode.removeChild(e)})),ft===yt&&(ft=0),Be&&(u&&(u.uncache=1),i=0,Vt.forEach((function(e){return e.pin===Be&&i++})),i||(u.spacer=0)),t.onKill&&t.onKill(yt)},yt.enable(!1,!1),Ce&&Ce(yt),n&&n.add&&!v?Oe.delayedCall(.01,(function(){return D||f||yt.refresh()}))&&(v=.01)&&(D=f=0):yt.refresh()}else this.update=this.refresh=this.kill=C},ue.register=function e(t){
return Ne||(Oe=t||E(),F()&&window.document&&ue.enable(),Ne=mt),Ne},ue.defaults=function e(t){if(t)for(var n in t)Ot[n]=t[n];return Ot},ue.disable=function e(t,n){mt=0,Vt.forEach((function(e){return e[n?"kill":"disable"](t)})),z(Le,"wheel",U),z(Ve,"scroll",U),clearInterval(je),z(Ve,"touchcancel",C),z(Ie,"touchstart",C),Y(z,Ve,"pointerdown,touchstart,mousedown",m),Y(z,Ve,"pointerup,touchend,mouseup",v),Xe.kill(),S(z);for(var r=0;r<we.length;r+=3)H(z,we[r],we[r+1]),H(z,we[r],we[r+2])},ue.enable=function e(){if(Le=window,Ve=document,Re=Ve.documentElement,Ie=Ve.body,Oe&&(ze=Oe.utils.toArray,He=Oe.utils.clamp,et=Oe.core.suppressOverwrites||C,Oe.core.globals("ScrollTrigger",ue),Ie)){mt=1,Pe.register(Oe),ue.isTouch=Pe.isTouch,ot=Pe.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),X(Le,"wheel",U),Ye=[Le,Ve,Re,Ie],ue.matchMedia({"(orientation: portrait)":function e(){return G(),G}}),X(Ve,"scroll",U);var t,n,r=Ie.style,i=r.borderTopStyle;for(r.borderTopStyle="solid",t=Mt(Ie),Me.m=Math.round(t.top+Me.sc())||0,ke.m=Math.round(t.left+ke.sc())||0,i?r.borderTopStyle=i:r.removeProperty("border-top-style"),je=setInterval(q,250),Oe.delayedCall(.5,(function(){return dt=0})),X(Ve,"touchcancel",C),X(Ie,"touchstart",C),Y(X,Ve,"pointerdown,touchstart,mousedown",m),Y(X,Ve,"pointerup,touchend,mouseup",v),Ge=Oe.utils.checkPrefix("transform"),$t.push(Ge),Ne=pt(),Xe=Oe.delayedCall(.2,Ut).pause(),Je=[Ve,"visibilitychange",function(){var e=Le.innerWidth,t=Le.innerHeight;Ve.hidden?(Ze=e,$e=t):Ze===e&&$e===t||K()},Ve,"DOMContentLoaded",Ut,Le,"load",Ut,Le,"resize",K],S(X),Vt.forEach((function(e){return e.enable(0,1)})),n=0;n<we.length;n+=3)H(z,we[n],we[n+1]),H(z,we[n],we[n+2])}},ue.config=function e(t){"limitCallbacks"in t&&(st=!!t.limitCallbacks);var n=t.syncInterval;n&&clearInterval(je)||(je=n)&&setInterval(q,n),"ignoreMobileResize"in t&&(rt=1===ue.isTouch&&t.ignoreMobileResize),"autoRefreshEvents"in t&&(S(z)||S(X,t.autoRefreshEvents||"none"),tt=-1===(t.autoRefreshEvents+"").indexOf("resize"))},ue.scrollerProxy=function e(t,n){var r=l(t),i=we.indexOf(r),u=x(r);~i&&we.splice(i,u?6:2),n&&(u?Se.unshift(Le,n,Ie,n,Re,n):Se.unshift(r,n))},ue.matchMedia=function e(t){var n,r,i,u,o;for(r in t)i=Xt.indexOf(r),u=t[r],"all"===(lt=r)?u():(n=Le.matchMedia(r))&&(n.matches&&(o=u()),~i?(Xt[i+1]=M(Xt[i+1],u),Xt[i+2]=M(Xt[i+2],o)):(i=Xt.length,Xt.push(r,u,o),n.addListener?n.addListener(Z):n.addEventListener("change",Z)),Xt[i+3]=n.matches),lt=0;return Xt},ue.clearMatchMedia=function e(t){t||(Xt.length=0),0<=(t=Xt.indexOf(t))&&Xt.splice(t,4)},ue.isInViewport=function e(t,n,r){var i=(B(t)?l(t):t).getBoundingClientRect(),u=i[r?Ft:Et]*n||0;return r?0<i.right-u&&i.left+u<Le.innerWidth:0<i.bottom-u&&i.top+u<Le.innerHeight},ue.positionInViewport=function e(t,n,r){B(t)&&(t=l(t));var i=t.getBoundingClientRect(),u=i[r?Ft:Et],o=null==n?u/2:n in Nt?Nt[n]*u:~n.indexOf("%")?parseFloat(n)*u/100:parseFloat(n)||0;return r?(i.left+o)/Le.innerWidth:(i.top+o)/Le.innerHeight},ue);nn.version="3.10.4",nn.saveStyles=function(e){return e?ze(e).forEach((function(e){if(e&&e.style){var t=Ht.indexOf(e);0<=t&&Ht.splice(t,5),Ht.push(e,e.style.cssText,e.getBBox&&e.getAttribute("transform"),Oe.core.getCache(e),lt)}})):Ht},nn.revert=function(e,t){return jt(!e,t)},nn.create=function(e,t){return new nn(e,t)},nn.refresh=function(e){return e?K():(Ne||nn.register())&&Ut(!0)},nn.update=Kt,nn.clearScrollMemory=Q,nn.maxScroll=function(e,t){return w(e,t?ke:Me)},nn.getScrollFunc=function(e,t){return c(l(e),t?ke:Me)},nn.getById=function(e){return Rt[e]},nn.getAll=function(){return Vt.filter((function(e){return"ScrollSmoother"!==e.vars.id}))},nn.isScrolling=function(){return!!gt},nn.snapDirectional=I,nn.addEventListener=function(e,t){var n=It[e]||(It[e]=[]);~n.indexOf(t)||n.push(t)},nn.removeEventListener=function(e,t){var n=It[e],r=n&&n.indexOf(t);0<=r&&n.splice(r,1)},nn.batch=function(e,t){function n(e,t){var n=[],r=[],i=Oe.delayedCall(o,(function(){t(n,r),n=[],r=[]})).pause();return function(e){n.length||i.restart(!0),n.push(e.trigger),r.push(e),s<=n.length&&i.progress(1)}}var r,i=[],u={},o=t.interval||.016,s=t.batchMax||1e9;for(r in t)u[r]="on"===r.substr(0,2)&&A(t[r])&&"onRefreshInit"!==r?n(0,t[r]):t[r];return A(s)&&(s=s(),X(nn,"refresh",(function(){return s=t.batchMax()}))),ze(e).forEach((function(e){var t={};for(r in u)t[r]=u[r];t.trigger=e,i.push(nn.create(t))})),i};var rn,un={auto:1,scroll:1},on=/(input|label|select|textarea)/i,sn=function e(t){var n=on.test(t.target.tagName);(n||rn)&&(t._gsapAllow=!0,rn=n)};nn.sort=function(e){return Vt.sort(e||function(e,t){return-1e6*(e.vars.refreshPriority||0)+e.start-(t.start+-1e6*(t.vars.refreshPriority||0))})},nn.observe=function(e){return new Pe(e)},nn.normalizeScroll=function(e){if(void 0===e)return nt;if(!0===e&&nt)return nt.enable();if(!1===e)return nt&&nt.kill();var t=e instanceof Pe?e:ce(e);return nt&&nt.target===t.target&&nt.kill(),x(t.target)&&(nt=t),t},nn.core={_getVelocityProp:D,_inputObserver:le,_scrollers:we,_proxies:Se,bridge:{ss:function e(){gt||zt("scrollStart"),gt=pt()},ref:function e(){return qe}}},E()&&Oe.registerPlugin(nn),e.ScrollTrigger=nn,e.default=nn,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default})),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function n(){return"undefined"!=typeof window}function r(){return u||n()&&(u=window.gsap)&&u.registerPlugin&&u}function i(e){function t(){return te.update(-J)}function n(){return _.style.overflow="visible"}function r(e){e.update();var t=e.getTween();t&&(t.pause(),t._time=t._dur,t._tTime=t._tDur),X=!1,e.animation.progress(e.progress,!0)}function v(e,t){(e!==J&&!R||t)&&(Z&&(_.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+e+", 0, 1)"),Q=e-J,J=e,d.isUpdating||d.update())}function C(e){return arguments.length?(R?J=-e:v(-e),ne.y=-e,X=!0,K(e),this):-J-K.offset}function y(e){T.scrollTop=0,U&&!1===U(A,e)||(d.isInViewport(e.target)||e.target===z||A.scrollTo(e.target,!1,"center center"),z=e.target)}function F(e){var t,n,r,i;P.forEach((function(o){t=o.pins,i=o.markers,e.forEach((function(e){e.trigger!==o.trigger&&e.pinnedContainer!==o.trigger||o===e||(n=e.start,r=(n-o.start-o.offset)/o.ratio-(n-o.start),t.forEach((function(e){return r-=e.distance/o.ratio-e.distance})),e.setPositions(n+r,e.end+r),e.markerStart&&i.push(u.quickSetter([e.markerStart,e.markerEnd],"y","px")),e.pin&&0<e.end&&(r=e.end-e.start,t.push({start:e.start,end:e.end,distance:r,trig:e}),o.setPositions(o.start,o.end+r),o.vars.onRefresh(o)))}))}))}function E(){n(),requestAnimationFrame(n),P&&(P.forEach((function(e){var t=e.start,n=e.auto?Math.min(d.maxScroll(e.scroller),e.end):t+(e.end-t)/e.ratio,r=(n-e.end)/2;t-=r,n-=r,e.offset=r||1e-4,e.pins.length=0,e.setPositions(Math.min(t,n),Math.max(t,n)),e.vars.onRefresh(e)})),F(d.sort())),te.reset()}function x(){return P&&P.forEach((function(e){return e.vars.onRefresh(e)}))}function b(){return P&&P.forEach((function(e){return e.vars.onRefreshInit(e)})),x}function w(e,t,n,r){return function(){var i="function"==typeof t?t(n,r):t;return i||0===i||(i=r.getAttribute("data-"+e)||("speed"===e?1:0)),r.setAttribute("data-"+e,i),"auto"===i?i:parseFloat(i)}}function S(e,t,n,r){function i(){t=C(),n=y(),c=parseFloat(t)||1,m=(p="auto"===t)?0:.5,g&&g.kill(),g=n&&u.to(e,{ease:h,overwrite:!1,y:"+=0",duration:n}),D&&(D.ratio=c,D.autoSpeed=p)}function o(){E.y=F+"px",E.renderTransform(1),i()}function a(t){if(p){o();var n=function e(t,n){var r,i,u=t.parentNode||l,o=t.getBoundingClientRect(),a=u.getBoundingClientRect(),c=a.top-o.top,D=a.bottom-o.bottom,f=(Math.abs(c)>Math.abs(D)?c:D)/(1-n),d=-f*n;return 0<f&&(d+=-(i=.5==(r=a.height/(s.innerHeight+a.height))?2*a.height:2*Math.min(a.height,-f*r/(2*r-1)))/2,f+=i),{change:f,offset:d}}(e,f(0,1,-t.start/(t.end-t.start)));B=n.change,v=n.offset}else B=(t.end-t.start)*(1-c),v=0;x.forEach((function(e){return B-=e.distance*(1-c)})),t.vars.onUpdate(t),g&&g.progress(1)}var c,D,p,g,m,v,C=w("speed",t,r,e),y=w("lag",n,r,e),F=u.getProperty(e,"y"),E=e._gsap,x=[],S=[],B=0;return i(),(1!==c||p||g)&&(a(D=d.create({trigger:p?e.parentNode:e,scroller:T,scrub:!0,refreshPriority:-999,onRefreshInit:o,onRefresh:a,onKill:function e(t){var n=P.indexOf(t);0<=n&&P.splice(n,1),o()},onUpdate:function e(t){var n,r,i,o=F+B*(t.progress-m),s=x.length,a=0;if(t.offset){if(s){for(r=-J,i=t.end;s--;){if((n=x[s]).trig.isActive||r>=n.start&&r<=n.end)return void(g&&(n.trig.progress+=n.trig.direction<0?.001:-.001,n.trig.update(0,0,1),g.resetTo("y",parseFloat(E.y),-Q,!0),ee&&g.progress(1)));r>n.end&&(a+=n.distance),i-=n.distance}o=F+a+B*((u.utils.clamp(t.start,t.end,r)-t.start-a)/(i-t.start)-m)}o=function e(t){return Math.round(1e5*t)/1e5||0}(o+v),S.length&&!p&&S.forEach((function(e){return e(o-a)})),g?(g.resetTo("y",o,-Q,!0),ee&&g.progress(1)):(E.y=o+"px",E.renderTransform(1))}}})),u.core.getCache(D.trigger).stRevert=b,D.startY=F,D.pins=x,D.markers=S,D.ratio=c,D.autoSpeed=p,e.style.willChange="transform"),D}function B(){return k=_.clientHeight,_.style.overflow="visible",c.style.height=k+"px",k-s.innerHeight}var A=this;o||i.register(u)||console.warn("Please gsap.registerPlugin(ScrollSmoother)"),e=this.vars=e||{},p&&p.kill(),p=this;var _,T,k,M,P,O,N,L,V,R,I,Y,X,z,H=e.smoothTouch,W=e.onUpdate,j=e.onStop,q=e.smooth,U=e.onFocusIn,G=e.normalizeScroll,K=d.getScrollFunc(s),Z=1===d.isTouch?!0===H?.8:parseFloat(H)||0:0===q||!1===q?0:parseFloat(q)||.8,J=0,Q=0,ee=1,te=g(0),ne={y:0};d.addEventListener("refresh",E),u.delayedCall(.5,(function(){return ee=0})),this.scrollTop=C,this.scrollTo=function(e,t,n){var r=u.utils.clamp(0,d.maxScroll(s),isNaN(e)?A.offset(e,n):+e);t?R?u.to(A,{duration:Z,scrollTop:r,overwrite:"auto",ease:h}):K(r):C(r)},this.offset=function(e,t){var n,r=(e=D(e)[0]).style.cssText,i=d.create({trigger:e,start:t||"top top"});return P&&F([i]),n=i.start,i.kill(!1),e.style.cssText=r,u.core.getCache(e).uncache=1,n},this.content=function(e){if(arguments.length){var t=D(e||"#smooth-content")[0]||c.children[0];return t!==_&&(V=(_=t).getAttribute("style")||"",u.set(_,{overflow:"visible",width:"100%",boxSizing:"border-box"})),this}return _},this.wrapper=function(e){return arguments.length?(T=D(e||"#smooth-wrapper")[0]||function e(t){var n=a.querySelector(".ScrollSmoother-wrapper");return n||((n=a.createElement("div")).classList.add("ScrollSmoother-wrapper"),t.parentNode.insertBefore(n,t),n.appendChild(t)),n}(_),L=T.getAttribute("style")||"",B(),u.set(T,Z?{overflow:"hidden",position:"fixed",height:"100%",width:"100%",top:0,left:0,right:0,bottom:0}:{overflow:"visible",position:"relative",width:"100%",height:"auto",top:"auto",bottom:"auto",left:"auto",right:"auto"}),this):T},this.effects=function(e,t){if(P=P||[],!e)return P.slice(0);(e=D(e)).forEach((function(e){for(var t=P.length;t--;)P[t].trigger===e&&(P[t].kill(),P.splice(t,1))}));var n,r,i=(t=t||{}).speed,u=t.lag,o=[];for(n=0;n<e.length;n++)(r=S(e[n],i,u,n))&&o.push(r);return P.push.apply(P,o),o},this.sections=function(e,t){if(O=O||[],!e)return O.slice(0);var n=D(e).map((function(e){return d.create({trigger:e,start:"top 120%",end:"bottom -20%",onToggle:function t(n){e.style.opacity=n.isActive?"1":"0",e.style.pointerEvents=n.isActive?"all":"none"}})}));return t&&t.add?O.push.apply(O,n):O=n.slice(0),n},this.content(e.content),this.wrapper(e.wrapper),this.render=function(e){return v(e||0===e?e:J)},this.getVelocity=function(){return te.getVelocity(-J)},d.scrollerProxy(T,{scrollTop:C,scrollHeight:function e(){return B()&&c.scrollHeight},fixedMarkers:!1!==e.fixedMarkers&&!!Z,content:_,getBoundingClientRect:function e(){return{top:0,left:0,width:s.innerWidth,height:s.innerHeight}}}),d.defaults({scroller:T});var re=d.getAll().filter((function(e){return e.scroller===s||e.scroller===T}));re.forEach((function(e){return e.revert(!0)})),M=d.create({animation:u.to(ne,{y:function e(){return-B()},ease:"none",data:"ScrollSmoother",duration:100,onUpdate:function e(){var n=X;n&&(ne.y=J,r(M)),v(ne.y,n),t(),W&&!R&&W(A)}}),onRefreshInit:function e(){Y=J,ne.y=T.scrollTop=0},id:"ScrollSmoother",scroller:s,invalidateOnRefresh:!0,start:0,refreshPriority:-9999,end:B,onScrubComplete:function e(){te.reset(),j&&j(A)},scrub:Z||!0,onRefresh:function e(t){r(t),ne.y=-K(),v(ne.y),ee||t.animation.progress(u.utils.clamp(0,1,Y/-t.end))}}),this.smooth=function(e){return Z=e,arguments.length?M.scrubDuration(e):M.getTween()?M.getTween().duration():0},M.getTween()&&(M.getTween().vars.ease=e.ease||h),this.scrollTrigger=M,e.effects&&this.effects(!0===e.effects?"[data-speed], [data-lag]":e.effects,{}),e.sections&&this.sections(!0===e.sections?"[data-section]":e.sections),re.forEach((function(e){e.vars.scroller=T,e.init(e.vars,e.animation)})),this.paused=function(e,t){return arguments.length?(!!R!==e&&(e?(M.getTween()&&M.getTween().pause(),K(-J),te.reset(),(I=d.normalizeScroll())&&I.disable(),(R=d.observe({preventDefault:!0,type:"wheel,touch,scroll",debounce:!1,allowClicks:!0,onChangeY:function e(){return C(-J)}})).nested=m(l,"wheel,touch,scroll",!0,!1!==t)):(R.nested.kill(),R.kill(),R=0,I&&I.enable(),M.progress=(-J-M.start)/(M.end-M.start),r(M))),this):!!R},this.kill=function(){A.paused(!1),r(M),M.kill();for(var e=(P||[]).concat(O||[]),t=e.length;t--;)e[t].kill();d.scrollerProxy(T),d.removeEventListener("refresh",E),c.style.removeProperty("height"),T.style.cssText=L,_.style.cssText=V;var n=d.defaults({});n&&n.scroller===T&&d.defaults({scroller:s}),A.normalizer&&d.normalizeScroll(!1),clearInterval(N),p=null,s.removeEventListener("focusin",y)},this.refresh=function(e,t){return M.refresh(e,t)},G&&(this.normalizer=d.normalizeScroll(!0===G?{debounce:!0,content:_}:G)),d.config(e),"overscrollBehavior"in s.getComputedStyle(c)&&u.set([c,l],{overscrollBehavior:"none"}),"scrollBehavior"in s.getComputedStyle(c)&&u.set([c,l],{scrollBehavior:"auto"}),s.addEventListener("focusin",y),N=setInterval(t,250),"loading"===a.readyState||requestAnimationFrame((function(){return d.refresh()}))}var u,o,s,a,l,c,D,f,d,p,h,g,m,v=(i.register=function e(t){return o||(u=t||r(),n()&&window.document&&(s=window,a=document,l=a.documentElement,c=a.body),u&&(D=u.utils.toArray,f=u.utils.clamp,h=u.parseEase("expo"),d=u.core.globals().ScrollTrigger,u.core.globals("ScrollSmoother",i),c&&d&&(g=d.core._getVelocityProp,m=d.core._inputObserver,i.refresh=d.refresh,o=1))),o},function e(n,r,i){r&&t(n.prototype,r),i&&t(n,i)}(i,[{key:"progress",get:function e(){return this.scrollTrigger.animation._time/100}}]),i);v.version="3.10.4",v.create=function(e){return p&&e&&p.content()===D(e.content)[0]?p:new v(e)},v.get=function(){return p},r()&&u.registerPlugin(v),e.ScrollSmoother=v,e.default=v,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default})),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(e){return m.getComputedStyle(e)}function n(e,t){var n;return E(e)?e:"string"==(n=typeof e)&&!t&&e?x.call(g.querySelectorAll(e),0):e&&"object"==n&&"length"in e?x.call(e,0):e?[e]:[]}function r(e){return"absolute"===e.position||!0===e.absolute}function i(e,t){for(var n,r=t.length;-1<--r;)if(n=t[r],e.substr(0,n.length)===n)return n.length}function u(e,t){void 0===e&&(e="");var n=~e.indexOf("++"),r=1;return n&&(e=e.split("++").join("")),function(){return"<"+t+" style='position:relative;display:inline-block;'"+(e?" class='"+e+(n?r++:"")+"'>":">")}}function o(e,t,n){var r=e.nodeType;if(1===r||9===r||11===r)for(e=e.firstChild;e;e=e.nextSibling)o(e,t,n);else 3!==r&&4!==r||(e.nodeValue=e.nodeValue.split(t).join(n))}function s(e,t){for(var n=t.length;-1<--n;)e.push(t[n])}function a(e,t,n){for(var r;e&&e!==t;){if(r=e._next||e.nextSibling)return r.textContent.charAt(0)===n;e=e.parentNode||e._parent}}function l(e){var t,r,i=n(e.childNodes),u=i.length;for(t=0;t<u;t++)(r=i[t])._isSplit?l(r):t&&r.previousSibling&&3===r.previousSibling.nodeType?(r.previousSibling.nodeValue+=3===r.nodeType?r.nodeValue:r.firstChild.nodeValue,e.removeChild(r)):3!==r.nodeType&&(e.insertBefore(r.firstChild,r),e.removeChild(r))}function c(e,t){return parseFloat(t[e])||0}function D(e,n,i,u,D,f,d){var p,h,m,v,C,y,F,E,x,b,w,S,B=t(e),A=c("paddingLeft",B),_=-999,T=c("borderBottomWidth",B)+c("borderTopWidth",B),k=c("borderLeftWidth",B)+c("borderRightWidth",B),M=c("paddingTop",B)+c("paddingBottom",B),P=c("paddingLeft",B)+c("paddingRight",B),O=c("fontSize",B)*(n.lineThreshold||.2),N=B.textAlign,L=[],V=[],R=[],I=n.wordDelimiter||" ",Y=n.tag?n.tag:n.span?"span":"div",X=n.type||n.split||"chars,words,lines",z=D&&~X.indexOf("lines")?[]:null,H=~X.indexOf("words"),W=~X.indexOf("chars"),j=r(n),q=n.linesClass,U=~(q||"").indexOf("++"),G=[],K="flex"===B.display,Z=e.style.display;for(U&&(q=q.split("++").join("")),K&&(e.style.display="block"),m=(h=e.getElementsByTagName("*")).length,C=[],p=0;p<m;p++)C[p]=h[p];if(z||j)for(p=0;p<m;p++)((y=(v=C[p]).parentNode===e)||j||W&&!H)&&(S=v.offsetTop,z&&y&&Math.abs(S-_)>O&&("BR"!==v.nodeName||0===p)&&(F=[],z.push(F),_=S),j&&(v._x=v.offsetLeft,v._y=S,v._w=v.offsetWidth,v._h=v.offsetHeight),z&&((v._isSplit&&y||!W&&y||H&&y||!H&&v.parentNode.parentNode===e&&!v.parentNode._isSplit)&&(F.push(v),v._x-=A,a(v,e,I)&&(v._wordEnd=!0)),"BR"===v.nodeName&&(v.nextSibling&&"BR"===v.nextSibling.nodeName||0===p)&&z.push([])));for(p=0;p<m;p++)if(y=(v=C[p]).parentNode===e,"BR"!==v.nodeName)if(j&&(x=v.style,H||y||(v._x+=v.parentNode._x,v._y+=v.parentNode._y),x.left=v._x+"px",x.top=v._y+"px",x.position="absolute",x.display="block",x.width=v._w+1+"px",x.height=v._h+"px"),!H&&W)if(v._isSplit)for(v._next=h=v.nextSibling,v.parentNode.appendChild(v);h&&3===h.nodeType&&" "===h.textContent;)v._next=h.nextSibling,v.parentNode.appendChild(h),h=h.nextSibling;else v.parentNode._isSplit?(v._parent=v.parentNode,!v.previousSibling&&v.firstChild&&(v.firstChild._isFirst=!0),v.nextSibling&&" "===v.nextSibling.textContent&&!v.nextSibling.nextSibling&&G.push(v.nextSibling),v._next=v.nextSibling&&v.nextSibling._isFirst?null:v.nextSibling,v.parentNode.removeChild(v),C.splice(p--,1),m--):y||(S=!v.nextSibling&&a(v.parentNode,e,I),v.parentNode._parent&&v.parentNode._parent.appendChild(v),S&&v.parentNode.appendChild(g.createTextNode(" ")),"span"===Y&&(v.style.display="inline"),L.push(v));else v.parentNode._isSplit&&!v._isSplit&&""!==v.innerHTML?V.push(v):W&&!v._isSplit&&("span"===Y&&(v.style.display="inline"),L.push(v));else z||j?(v.parentNode&&v.parentNode.removeChild(v),C.splice(p--,1),m--):H||e.appendChild(v);for(p=G.length;-1<--p;)G[p].parentNode.removeChild(G[p]);if(z){for(j&&(b=g.createElement(Y),e.appendChild(b),w=b.offsetWidth+"px",S=b.offsetParent===e?0:e.offsetLeft,e.removeChild(b)),x=e.style.cssText,e.style.cssText="display:none;";e.firstChild;)e.removeChild(e.firstChild);for(E=" "===I&&(!j||!H&&!W),p=0;p<z.length;p++){for(F=z[p],(b=g.createElement(Y)).style.cssText="display:block;text-align:"+N+";position:"+(j?"absolute;":"relative;"),q&&(b.className=q+(U?p+1:"")),R.push(b),m=F.length,h=0;h<m;h++)"BR"!==F[h].nodeName&&(v=F[h],b.appendChild(v),E&&v._wordEnd&&b.appendChild(g.createTextNode(" ")),j&&(0===h&&(b.style.top=v._y+"px",b.style.left=A+S+"px"),v.style.top="0px",S&&(v.style.left=v._x-S+"px")));0===m?b.innerHTML="&nbsp;":H||W||(l(b),o(b,String.fromCharCode(160)," ")),j&&(b.style.width=w,b.style.height=v._h+"px"),e.appendChild(b)}e.style.cssText=x}j&&(d>e.clientHeight&&(e.style.height=d-M+"px",e.clientHeight<d&&(e.style.height=d+T+"px")),f>e.clientWidth&&(e.style.width=f-P+"px",e.clientWidth<f&&(e.style.width=f+k+"px"))),K&&(Z?e.style.display=Z:e.style.removeProperty("display")),s(i,L),H&&s(u,V),s(D,R)}function f(e,t,n,u){var s,a,l,c,D,f,d,p,m=t.tag?t.tag:t.span?"span":"div",v=~(t.type||t.split||"chars,words,lines").indexOf("chars"),C=r(t),E=t.wordDelimiter||" ",x=" "!==E?"":C?"&#173; ":" ",b="</"+m+">",w=1,S=t.specialChars?"function"==typeof t.specialChars?t.specialChars:i:null,B=g.createElement("div"),A=e.parentNode;for(A.insertBefore(B,e),B.textContent=e.nodeValue,A.removeChild(e),d=-1!==(s=function e(t){var n=t.nodeType,r="";if(1===n||9===n||11===n){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)r+=e(t)}else if(3===n||4===n)return t.nodeValue;return r}(e=B)).indexOf("<"),!1!==t.reduceWhiteSpace&&(s=s.replace(F," ").replace(y,"")),d&&(s=s.split("<").join("{{LT}}")),D=s.length,a=(" "===s.charAt(0)?x:"")+n(),l=0;l<D;l++)if(f=s.charAt(l),S&&(p=S(s.substr(l),t.specialChars)))f=s.substr(l,p||1),a+=v&&" "!==f?u()+f+"</"+m+">":f,l+=p-1;else if(f===E&&s.charAt(l-1)!==E&&l){for(a+=w?b:"",w=0;s.charAt(l+1)===E;)a+=x,l++;l===D-1?a+=x:")"!==s.charAt(l+1)&&(a+=x+n(),w=1)}else"{"===f&&"{{LT}}"===s.substr(l,6)?(a+=v?u()+"{{LT}}</"+m+">":"{{LT}}",l+=5):55296<=f.charCodeAt(0)&&f.charCodeAt(0)<=56319||65024<=s.charCodeAt(l+1)&&s.charCodeAt(l+1)<=65039?(c=((s.substr(l,12).split(h)||[])[1]||"").length||2,a+=v&&" "!==f?u()+s.substr(l,c)+"</"+m+">":s.substr(l,c),l+=c-1):a+=v&&" "!==f?u()+f+"</"+m+">":f;e.outerHTML=a+(w?b:""),d&&o(A,"{{LT}}","<")}function d(e,i,u,o){var s,a,l=n(e.childNodes),c=l.length,D=r(i);if(3!==e.nodeType||1<c){for(i.absolute=!1,s=0;s<c;s++)(a=l[s])._next=a._isFirst=a._parent=a._wordEnd=null,3===a.nodeType&&!/\S+/.test(a.nodeValue)||(D&&3!==a.nodeType&&"inline"===t(a).display&&(a.style.display="inline-block",a.style.position="relative"),a._isSplit=!0,d(a,i,u,o));return i.absolute=D,void(e._isSplit=!0)}f(e,i,u,o)}function p(e,t){v||function e(){g=document,m=window,v=1}(),this.elements=n(e),this.chars=[],this.words=[],this.lines=[],this._originals=[],this.vars=t||{},this.split(t)}var h=/([\uD800-\uDBFF][\uDC00-\uDFFF](?:[\u200D\uFE0F][\uD800-\uDBFF][\uDC00-\uDFFF]){2,}|\uD83D\uDC69(?:\u200D(?:(?:\uD83D\uDC69\u200D)?\uD83D\uDC67|(?:\uD83D\uDC69\u200D)?\uD83D\uDC66)|\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC69\u200D(?:\uD83D\uDC69\u200D)?\uD83D\uDC66\u200D\uD83D\uDC66|\uD83D\uDC69\u200D(?:\uD83D\uDC69\u200D)?\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C\uDFF3\uFE0F\u200D\uD83C\uDF08|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642]\uFE0F|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC6F\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3C-\uDD3E\uDDD6-\uDDDF])\u200D[\u2640\u2642]\uFE0F|\uD83C\uDDFD\uD83C\uDDF0|\uD83C\uDDF6\uD83C\uDDE6|\uD83C\uDDF4\uD83C\uDDF2|\uD83C\uDDE9(?:\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF])|\uD83C\uDDF7(?:\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC])|\uD83C\uDDE8(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uFE0F\u200D[\u2640\u2642]|(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2640\u2642])\uFE0F|(?:\uD83D\uDC41\uFE0F\u200D\uD83D\uDDE8|\uD83D\uDC69(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2695\u2696\u2708]|\uD83D\uDC69\u200D[\u2695\u2696\u2708]|\uD83D\uDC68(?:(?:\uD83C[\uDFFB-\uDFFF])\u200D[\u2695\u2696\u2708]|\u200D[\u2695\u2696\u2708]))\uFE0F|\uD83C\uDDF2(?:\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF])|\uD83D\uDC69\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D(?:\uD83D[\uDC68\uDC69])|\uD83D[\uDC68\uDC69]))|\uD83C\uDDF1(?:\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE])|\uD83C\uDDEF(?:\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5])|\uD83C\uDDED(?:\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA])|\uD83C\uDDEB(?:\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7])|[#\*0-9]\uFE0F\u20E3|\uD83C\uDDE7(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF])|\uD83C\uDDE6(?:\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF])|\uD83C\uDDFF(?:\uD83C[\uDDE6\uDDF2\uDDFC])|\uD83C\uDDF5(?:\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE])|\uD83C\uDDFB(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA])|\uD83C\uDDF3(?:\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF])|\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62(?:\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73|\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74|\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67)\uDB40\uDC7F|\uD83D\uDC68(?:\u200D(?:\u2764\uFE0F\u200D(?:\uD83D\uDC8B\u200D)?\uD83D\uDC68|(?:(?:\uD83D[\uDC68\uDC69])\u200D)?\uD83D\uDC66\u200D\uD83D\uDC66|(?:(?:\uD83D[\uDC68\uDC69])\u200D)?\uD83D\uDC67\u200D(?:\uD83D[\uDC66\uDC67])|\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92])|(?:\uD83C[\uDFFB-\uDFFF])\u200D(?:\uD83C[\uDF3E\uDF73\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]))|\uD83C\uDDF8(?:\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF])|\uD83C\uDDF0(?:\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF])|\uD83C\uDDFE(?:\uD83C[\uDDEA\uDDF9])|\uD83C\uDDEE(?:\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9])|\uD83C\uDDF9(?:\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF])|\uD83C\uDDEC(?:\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE])|\uD83C\uDDFA(?:\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF])|\uD83C\uDDEA(?:\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA])|\uD83C\uDDFC(?:\uD83C[\uDDEB\uDDF8])|(?:\u26F9|\uD83C[\uDFCB\uDFCC]|\uD83D\uDD75)(?:\uD83C[\uDFFB-\uDFFF])|(?:\uD83C[\uDFC3\uDFC4\uDFCA]|\uD83D[\uDC6E\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6]|\uD83E[\uDD26\uDD37-\uDD39\uDD3D\uDD3E\uDDD6-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u270A-\u270D]|\uD83C[\uDF85\uDFC2\uDFC7]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC70\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDCAA\uDD74\uDD7A\uDD90\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC]|\uD83E[\uDD18-\uDD1C\uDD1E\uDD1F\uDD30-\uDD36\uDDD1-\uDDD5])(?:\uD83C[\uDFFB-\uDFFF])|\uD83D\uDC68(?:\u200D(?:(?:(?:\uD83D[\uDC68\uDC69])\u200D)?\uD83D\uDC67|(?:(?:\uD83D[\uDC68\uDC69])\u200D)?\uD83D\uDC66)|\uD83C[\uDFFB-\uDFFF])|(?:[\u261D\u26F9\u270A-\u270D]|\uD83C[\uDF85\uDFC2-\uDFC4\uDFC7\uDFCA-\uDFCC]|\uD83D[\uDC42\uDC43\uDC46-\uDC50\uDC66-\uDC69\uDC6E\uDC70-\uDC78\uDC7C\uDC81-\uDC83\uDC85-\uDC87\uDCAA\uDD74\uDD75\uDD7A\uDD90\uDD95\uDD96\uDE45-\uDE47\uDE4B-\uDE4F\uDEA3\uDEB4-\uDEB6\uDEC0\uDECC]|\uD83E[\uDD18-\uDD1C\uDD1E\uDD1F\uDD26\uDD30-\uDD39\uDD3D\uDD3E\uDDD1-\uDDDD])(?:\uD83C[\uDFFB-\uDFFF])?|(?:[\u231A\u231B\u23E9-\u23EC\u23F0\u23F3\u25FD\u25FE\u2614\u2615\u2648-\u2653\u267F\u2693\u26A1\u26AA\u26AB\u26BD\u26BE\u26C4\u26C5\u26CE\u26D4\u26EA\u26F2\u26F3\u26F5\u26FA\u26FD\u2705\u270A\u270B\u2728\u274C\u274E\u2753-\u2755\u2757\u2795-\u2797\u27B0\u27BF\u2B1B\u2B1C\u2B50\u2B55]|\uD83C[\uDC04\uDCCF\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF93\uDFA0-\uDFCA\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF4\uDFF8-\uDFFF]|\uD83D[\uDC00-\uDC3E\uDC40\uDC42-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDD7A\uDD95\uDD96\uDDA4\uDDFB-\uDE4F\uDE80-\uDEC5\uDECC\uDED0-\uDED2\uDEEB\uDEEC\uDEF4-\uDEF8]|\uD83E[\uDD10-\uDD3A\uDD3C-\uDD3E\uDD40-\uDD45\uDD47-\uDD4C\uDD50-\uDD6B\uDD80-\uDD97\uDDC0\uDDD0-\uDDE6])|(?:[#\*0-9\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23E9-\u23F3\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB-\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u261D\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u2660\u2663\u2665\u2666\u2668\u267B\u267F\u2692-\u2697\u2699\u269B\u269C\u26A0\u26A1\u26AA\u26AB\u26B0\u26B1\u26BD\u26BE\u26C4\u26C5\u26C8\u26CE\u26CF\u26D1\u26D3\u26D4\u26E9\u26EA\u26F0-\u26F5\u26F7-\u26FA\u26FD\u2702\u2705\u2708-\u270D\u270F\u2712\u2714\u2716\u271D\u2721\u2728\u2733\u2734\u2744\u2747\u274C\u274E\u2753-\u2755\u2757\u2763\u2764\u2795-\u2797\u27A1\u27B0\u27BF\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B50\u2B55\u3030\u303D\u3297\u3299]|\uD83C[\uDC04\uDCCF\uDD70\uDD71\uDD7E\uDD7F\uDD8E\uDD91-\uDD9A\uDDE6-\uDDFF\uDE01\uDE02\uDE1A\uDE2F\uDE32-\uDE3A\uDE50\uDE51\uDF00-\uDF21\uDF24-\uDF93\uDF96\uDF97\uDF99-\uDF9B\uDF9E-\uDFF0\uDFF3-\uDFF5\uDFF7-\uDFFF]|\uD83D[\uDC00-\uDCFD\uDCFF-\uDD3D\uDD49-\uDD4E\uDD50-\uDD67\uDD6F\uDD70\uDD73-\uDD7A\uDD87\uDD8A-\uDD8D\uDD90\uDD95\uDD96\uDDA4\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA-\uDE4F\uDE80-\uDEC5\uDECB-\uDED2\uDEE0-\uDEE5\uDEE9\uDEEB\uDEEC\uDEF0\uDEF3-\uDEF8]|\uD83E[\uDD10-\uDD3A\uDD3C-\uDD3E\uDD40-\uDD45\uDD47-\uDD4C\uDD50-\uDD6B\uDD80-\uDD97\uDDC0\uDDD0-\uDDE6])\uFE0F)/,g,m,v,C,y=/(?:\r|\n|\t\t)/g,F=/(?:\s\s+)/g,E=Array.isArray,x=[].slice,b=((C=p.prototype).split=function e(t){this.isSplit&&this.revert(),this.vars=t=t||this.vars,this._originals.length=this.chars.length=this.words.length=this.lines.length=0;for(var n,r,i,o=this.elements.length,s=t.tag?t.tag:t.span?"span":"div",a=u(t.wordsClass,s),l=u(t.charsClass,s);-1<--o;)i=this.elements[o],this._originals[o]=i.innerHTML,n=i.clientHeight,r=i.clientWidth,d(i,t,a,l),D(i,t,this.chars,this.words,this.lines,r,n);return this.chars.reverse(),this.words.reverse(),this.lines.reverse(),this.isSplit=!0,this},C.revert=function e(){var t=this._originals;if(!t)throw"revert() call wasn't scoped properly.";return this.elements.forEach((function(e,n){return e.innerHTML=t[n]})),this.chars=[],this.words=[],this.lines=[],this.isSplit=!1,this},p.create=function e(t,n){return new p(t,n)},p);b.version="3.10.4",e.SplitText=b,e.default=b,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default})),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(e){var t=e.ownerDocument||e;!(K in e.style)&&"msTransform"in e.style&&(Z=(K="msTransform")+"Origin");for(;t.parentNode&&(t=t.parentNode););if(Y=window,j=new ee,t){X=(I=t).documentElement,z=t.body,(q=I.createElementNS("http://www.w3.org/2000/svg","g")).style.transform="none";var n=t.createElement("div"),r=t.createElement("div");z.appendChild(n),n.appendChild(r),n.style.position="static",n.style[K]="translate3d(0,0,1px)",U=r.offsetParent!==n,z.removeChild(n)}return t}function n(){return Y.pageYOffset||I.scrollTop||X.scrollTop||z.scrollTop||0}function r(){return Y.pageXOffset||I.scrollLeft||X.scrollLeft||z.scrollLeft||0}function i(e){return e.ownerSVGElement||("svg"===(e.tagName+"").toLowerCase()?e:null)}function u(e,n){if(e.parentNode&&(I||t(e))){var r=i(e),o=r?r.getAttribute("xmlns")||"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",s=r?n?"rect":"g":"div",a=2!==n?0:100,l=3===n?100:0,c="position:absolute;display:block;pointer-events:none;margin:0;padding:0;",D=I.createElementNS?I.createElementNS(o.replace(/^https/,"http"),s):I.createElement(s);return n&&(r?(W=W||u(e),D.setAttribute("width",.01),D.setAttribute("height",.01),D.setAttribute("transform","translate("+a+","+l+")"),W.appendChild(D)):(H||((H=u(e)).style.cssText=c),D.style.cssText=c+"width:0.1px;height:0.1px;top:"+l+"px;left:"+a+"px",H.appendChild(D))),D}throw"Need document and parent."}function o(e){var t,n=e.getCTM();return n||(t=e.style[K],e.style[K]="none",e.appendChild(q),n=q.getCTM(),e.removeChild(q),t?e.style[K]=t:e.style.removeProperty(K.replace(/([A-Z])/g,"-$1").toLowerCase())),n||j.clone()}function s(e,t){
var n,r,s,a,l,c,D=i(e),f=e===D,d=D?J:Q,p=e.parentNode;if(e===Y)return e;if(d.length||d.push(u(e,1),u(e,2),u(e,3)),n=D?W:H,D)f?(a=-(s=o(e)).e/s.a,l=-s.f/s.d,r=j):e.getBBox?(s=e.getBBox(),a=(r=(r=e.transform?e.transform.baseVal:{}).numberOfItems?1<r.numberOfItems?function e(t){for(var n=new ee,r=0;r<t.numberOfItems;r++)n.multiply(t.getItem(r).matrix);return n}(r):r.getItem(0).matrix:j).a*s.x+r.c*s.y,l=r.b*s.x+r.d*s.y):(r=new ee,a=l=0),t&&"g"===e.tagName.toLowerCase()&&(a=l=0),(f?D:p).appendChild(n),n.setAttribute("transform","matrix("+r.a+","+r.b+","+r.c+","+r.d+","+(r.e+a)+","+(r.f+l)+")");else{if(a=l=0,U)for(r=e.offsetParent,s=e;(s=s&&s.parentNode)&&s!==r&&s.parentNode;)4<(Y.getComputedStyle(s)[K]+"").length&&(a=s.offsetLeft,l=s.offsetTop,s=0);if("absolute"!==(c=Y.getComputedStyle(e)).position&&"fixed"!==c.position)for(r=e.offsetParent;p&&p!==r;)a+=p.scrollLeft||0,l+=p.scrollTop||0,p=p.parentNode;(s=n.style).top=e.offsetTop-l+"px",s.left=e.offsetLeft-a+"px",s[K]=c[K],s[Z]=c[Z],s.position="fixed"===c.position?"fixed":"absolute",e.parentNode.appendChild(n)}return n}function a(e,t,n,r,i,u,o){return e.a=t,e.b=n,e.c=r,e.d=i,e.e=u,e.f=o,e}function l(e,t,n,r,i,u){void 0===e&&(e=1),void 0===t&&(t=0),void 0===n&&(n=0),void 0===r&&(r=1),void 0===i&&(i=0),void 0===u&&(u=0),a(this,e,t,n,r,i,u)}function c(e,u,o,a){if(!e||!e.parentNode||(I||t(e)).documentElement===e)return new ee;var l=function e(t){for(var n,r;t&&t!==z;)(r=t._gsap)&&r.uncache&&r.get(t,"x"),r&&!r.scaleX&&!r.scaleY&&r.renderTransform&&(r.scaleX=r.scaleY=1e-4,r.renderTransform(1,r),n?n.push(r):n=[r]),t=t.parentNode;return n}(e),c=i(e)?J:Q,D=s(e,o),f=c[0].getBoundingClientRect(),d=c[1].getBoundingClientRect(),p=c[2].getBoundingClientRect(),h=D.parentNode,g=!a&&function e(t){return"fixed"===Y.getComputedStyle(t).position||((t=t.parentNode)&&1===t.nodeType?e(t):void 0)}(e),m=new ee((d.left-f.left)/100,(d.top-f.top)/100,(p.left-f.left)/100,(p.top-f.top)/100,f.left+(g?0:r()),f.top+(g?0:n()));if(h.removeChild(D),l)for(f=l.length;f--;)(d=l[f]).scaleX=d.scaleY=0,d.renderTransform(1,d);return u?m.inverse():m}function D(e,t){return e.actions.forEach((function(e){return e.vars[t]&&e.vars[t](e)}))}function f(e){return"string"==typeof e?e.split(" ").join("").split(","):e}function d(e){return te(e)[0]||console.warn("Element not found:",e)}function p(e){return Math.round(1e4*e)/1e4||0}function h(e,t,n){return e.forEach((function(e){return e.classList[n](t)}))}function g(e){return e.replace(/([A-Z])/g,"-$1").toLowerCase()}function $(e,t){var n,r={};for(n in e)t[n]||(r[n]=e[n]);return r}function m(e){var t=ye[e]=f(e);return he[e]=t.concat(me),t}function v(e,t,n){return e.forEach((function(e){return e.d=function e(t,n,r){void 0===r&&(r=0);for(var i=t.parentNode,u=1e3*Math.pow(10,r)*(n?-1:1),o=n?900*-u:0;t;)o+=u,t=t.previousSibling;return i?o+e(i,n,r+1):o}(n?e.element:e.t,t)})),e.sort((function(e,t){return e.d-t.d})),e}function C(e,t){for(var n,r,i=e.element.style,u=e.css=e.css||[],o=t.length;o--;)r=i[n=t[o]]||i.getPropertyValue(n),u.push(r?n:pe[n]||(pe[n]=g(n)),r);return i}function y(e){var t=e.css,n=e.element.style,r=0;for(e.cache.uncache=1;r<t.length;r+=2)t[r+1]?n[t[r]]=t[r+1]:n.removeProperty(t[r])}function F(e,t){e.forEach((function(e){return e.a.cache.uncache=1})),t||e.finalStates.forEach(y)}function E(e,t,i){var u,o,s,a=e.element,l=e.width,D=e.height,f=e.uncache,d=e.getProp,p=a.style,h=4;if("object"!=typeof t&&(t=e),re&&1!==i)return re._abs.push({t:a,b:e,a:e,sd:0}),re._final.push((function(){return(e.cache.uncache=1)&&y(e)})),a;for(o="none"===d("display"),e.isVisible&&!o||(o&&(C(e,["display"]).display=t.display),e.matrix=t.matrix,e.width=l=e.width||t.width,e.height=D=e.height||t.height),C(e,Fe),s=window.getComputedStyle(a);h--;)p[Fe[h]]=s[Fe[h]];if(p.gridArea="1 / 1 / 1 / 1",p.transition="none",p.position="absolute",p.width=l+"px",p.height=D+"px",p.top||(p.top="0px"),p.left||(p.left="0px"),f)u=new Ae(a);else if((u=$(e,de)).position="absolute",e.simple){var g=a.getBoundingClientRect();u.matrix=new ee(1,0,0,1,g.left+r(),g.top+n())}else u.matrix=c(a,!1,!1,!0);return u=xe(u,e,!0),e.x=oe(u.x,.01),e.y=oe(u.y,.01),a}function x(e,t){return!0!==t&&(t=te(t),e=e.filter((function(e){if(-1!==t.indexOf((e.sd<0?e.b:e.a).element))return!0;e.t._gsap.renderTransform(1),e.t.style.width=e.b.width+"px",e.t.style.height=e.b.height+"px"}))),e}function b(e){return v(e,!0).forEach((function(e){return(e.a.isVisible||e.b.isVisible)&&E(e.sd<0?e.b:e.a,e.b,1)}))}function w(e,t){var n,r=e.style||e;for(n in t)r[n]=t[n]}function S(e){return e.map((function(e){return e.element}))}function B(e,t,n){return e&&t.length&&n.add(e(S(t),n,new Se(t,0,!0)),0)}function A(e,t){return e instanceof Se?e:new Se(e,t)}function _(e,t,n){var r=e.idLookup[n],i=e.alt[n];return!i.isVisible||(t.getElementState(i.element)||i).isVisible&&r.isVisible?r:i}function T(e){if(e!==se){var t=ue.style,n=ue.clientWidth===window.outerWidth,r=ue.clientHeight===window.outerHeight,i=4;if(e&&(n||r)){for(;i--;)be[i]=t[we[i]];n&&(t.width=ue.clientWidth+"px",t.overflowY="hidden"),r&&(t.height=ue.clientHeight+"px",t.overflowX="hidden"),se=e}else if(se){for(;i--;)be[i]?t[we[i]]=be[i]:t.removeProperty(g(we[i]));se=e}}}function k(e,t,n,r){e instanceof Se&&t instanceof Se||console.warn("Not a valid state object.");var i,u,o,s,a,l,D,f,d,p,g,y,E,w,S,A=(n=n||{}).clearProps,k=n.onEnter,M=n.onLeave,P=n.absolute,O=n.absoluteOnLeave,N=n.custom,L=n.delay,V=n.paused,R=n.repeat,I=n.repeatDelay,Y=n.yoyo,X=n.toggleClass,z=n.nested,H=n.zIndex,W=n.scale,j=n.fade,q=n.stagger,U=n.spin,G=n.prune,K=("props"in n?n:e).props,Z=$(n,ve),J=ne.timeline({delay:L,paused:V,repeat:R,repeatDelay:I,yoyo:Y}),Q=Z,ee=[],te=[],ie=[],ue=[],oe=!0===U?1:U||0,se="function"==typeof U?U:function(){return oe},ae=e.interrupted||t.interrupted,le=J[1!==r?"to":"from"];for(u in t.idLookup)g=t.alt[u]?_(t,e,u):t.idLookup[u],a=g.element,p=e.idLookup[u],!e.alt[u]||a!==p.element||!e.alt[u].isVisible&&g.isVisible||(p=e.alt[u]),p?(l={t:a,b:p,a:g,sd:p.element===a?0:g.isVisible?1:-1},ie.push(l),l.sd&&(l.sd<0&&(l.b=g,l.a=p),ae&&C(l.b,K?he[K]:me),j&&ie.push(l.swap={t:p.element,b:l.b,a:l.a,sd:-l.sd,swap:l})),a._flip=p.element._flip=re?re.timeline:J):g.isVisible&&(ie.push({t:a,b:$(g,{isVisible:1}),a:g,sd:0,entering:1}),a._flip=re?re.timeline:J);return K&&(ye[K]||m(K)).forEach((function(e){return Z[e]=function(t){return ie[t].a.props[e]}})),ie.finalStates=d=[],y=function t(){for(v(ie),T(!0),s=0;s<ie.length;s++)l=ie[s],E=l.a,w=l.b,!G||E.isDifferent(w)||l.entering?(a=l.t,!z||l.sd<0||!s||(E.matrix=c(a,!1,!1,!0)),l.sd||w.isVisible&&E.isVisible?(l.sd<0?(D=new Ae(a,K,e.simple),xe(D,E,W,0,0,D),D.matrix=c(a,!1,!1,!0),D.css=l.b.css,l.a=E=D,j&&(a.style.opacity=ae?w.opacity:E.opacity),q&&ue.push(a)):0<l.sd&&j&&(a.style.opacity=ae?E.opacity-w.opacity:"0"),xe(E,w,W,K)):w.isVisible!==E.isVisible&&(w.isVisible?E.isVisible||(w.css=E.css,te.push(w),ie.splice(s--,1),P&&z&&xe(E,w,W,K)):(E.isVisible&&ee.push(E),ie.splice(s--,1))),W||(a.style.maxWidth=Math.max(E.width,w.width)+"px",a.style.maxHeight=Math.max(E.height,w.height)+"px",a.style.minWidth=Math.min(E.width,w.width)+"px",a.style.minHeight=Math.min(E.height,w.height)+"px"),z&&X&&a.classList.add(X)):ie.splice(s--,1),d.push(E);var r;if(X&&(r=d.map((function(e){return e.element})),z&&r.forEach((function(e){return e.classList.remove(X)}))),T(!1),W?(Z.scaleX=function(e){return ie[e].a.scaleX},Z.scaleY=function(e){return ie[e].a.scaleY}):(Z.width=function(e){return ie[e].a.width+"px"},Z.height=function(e){return ie[e].a.height+"px"},Z.autoRound=n.autoRound||!1),Z.x=function(e){return ie[e].a.x+"px"},Z.y=function(e){return ie[e].a.y+"px"},Z.rotation=function(e){return ie[e].a.rotation+(U?360*se(e,f[e],f):0)},Z.skewX=function(e){return ie[e].a.skewX},f=ie.map((function(e){return e.t})),!H&&0!==H||(Z.modifiers={zIndex:function e(){return H}},Z.zIndex=H,Z.immediateRender=!1!==n.immediateRender),j&&(Z.opacity=function(e){return ie[e].sd<0?0:0<ie[e].sd?ie[e].a.opacity:"+=0"}),ue.length){q=ne.utils.distribute(q);var p=f.slice(ue.length);Z.stagger=function(e,t){return q(~ue.indexOf(t)?f.indexOf(ie[e].swap.t):e,t,p)}}if(ge.forEach((function(e){return n[e]&&J.eventCallback(e,n[e],n[e+"Params"])})),N&&f.length)for(u in Q=$(Z,ve),"scale"in N&&(N.scaleX=N.scaleY=N.scale,delete N.scale),N)(i=$(N[u],Ce))[u]=Z[u],!("duration"in i)&&"duration"in Z&&(i.duration=Z.duration),i.stagger=Z.stagger,le.call(J,f,i,0),delete Q[u];(f.length||te.length||ee.length)&&(X&&J.add((function(){return h(r,X,J._zTime<0?"remove":"add")}),0)&&!V&&h(r,X,"add"),f.length&&le.call(J,f,Q,0)),B(k,ee,J),B(M,te,J);var g=re&&re.timeline;g&&(g.add(J,0),re._final.push((function(){return F(ie,!A)}))),o=J.duration(),J.call((function(){var e=J.time()>=o;e&&!g&&F(ie,!A),X&&h(r,X,e?"remove":"add")}))},O&&(P=ie.filter((function(e){return!e.sd&&!e.a.isVisible&&e.b.isVisible})).map((function(e){return e.a.element}))),re?(P&&(S=re._abs).push.apply(S,x(ie,P)),re._run.push(y)):(P&&b(x(ie,P)),y()),re?re.timeline:J}function M(e,t){if(e&&e.progress()<1&&!e.paused())return t&&(function e(t){t.vars.onInterrupt&&t.vars.onInterrupt.apply(t,t.vars.onInterruptParams||[]),t.getChildren(!0,!1,!0).forEach(e)}(e),t<2&&e.progress(1),e.kill()),!0}function P(e){for(var t,n=e.idLookup={},r=e.alt={},i=e.elementStates,u=i.length;u--;)n[(t=i[u]).id]?r[t.id]=t:n[t.id]=t}function O(e,t,n){if(this.props=t&&t.props,this.simple=!(!t||!t.simple),n)this.targets=S(e),this.elementStates=e,P(this);else{this.targets=te(e);var r=t&&(!1===t.kill||t.batch&&!t.kill);re&&!r&&re._kill.push(this),this.update(r||!!re)}}function N(e,t,n){this.element=e,this.update(t,n)}function L(e,t){this.vars=e,this.batch=t,this.states=[],this.timeline=t.timeline}function V(e){this.id=e,this.actions=[],this._kill=[],this._final=[],this._abs=[],this._run=[],this.data={},this.state=new Se,this.timeline=ne.timeline()}function R(){}var I,Y,X,z,H,W,j,q,U,G,K="transform",Z=K+"Origin",J=[],Q=[],ee=((G=l.prototype).inverse=function e(){var t=this.a,n=this.b,r=this.c,i=this.d,u=this.e,o=this.f,s=t*i-n*r||1e-10;return a(this,i/s,-n/s,-r/s,t/s,(r*o-i*u)/s,-(t*o-n*u)/s)},G.multiply=function e(t){var n=this.a,r=this.b,i=this.c,u=this.d,o=this.e,s=this.f,l=t.a,c=t.c,D=t.b,f=t.d,d=t.e,p=t.f;return a(this,l*n+D*i,l*r+D*u,c*n+f*i,c*r+f*u,o+d*n+p*i,s+d*r+p*u)},G.clone=function e(){return new l(this.a,this.b,this.c,this.d,this.e,this.f)},G.equals=function e(t){var n=this.a,r=this.b,i=this.c,u=this.d,o=this.e,s=this.f;return n===t.a&&r===t.b&&i===t.c&&u===t.d&&o===t.e&&s===t.f},G.apply=function e(t,n){void 0===n&&(n={});var r=t.x,i=t.y,u=this.a,o=this.b,s=this.c,a=this.d,l=this.e,c=this.f;return n.x=r*u+i*s+l||0,n.y=r*o+i*a+c||0,n},l),te,ne,re,ie,ue,oe,se,ae,le=1,ce={},De=180/Math.PI,fe=Math.PI/180,de={},pe={},he={},ge=f("onStart,onUpdate,onComplete,onReverseComplete,onInterrupt"),me=f("transform,transformOrigin,width,height,position,top,left,opacity,zIndex,maxWidth,maxHeight,minWidth,minHeight"),ve={zIndex:1,kill:1,simple:1,spin:1,clearProps:1,targets:1,toggleClass:1,onComplete:1,onUpdate:1,onInterrupt:1,onStart:1,delay:1,repeat:1,repeatDelay:1,yoyo:1,scale:1,fade:1,absolute:1,props:1,onEnter:1,onLeave:1,custom:1,paused:1,nested:1,prune:1,absoluteOnLeave:1},Ce={zIndex:1,simple:1,clearProps:1,scale:1,absolute:1,fitChild:1,getVars:1,props:1},ye={},Fe="paddingTop,paddingRight,paddingBottom,paddingLeft,gridArea,transition".split(","),Ee=function e(t,n,r,i){return t instanceof Ae?t:t instanceof Se?function e(t,n){return n&&t.idLookup[Ee(n).id]||t.elementStates[0]}(t,i):new Ae("string"==typeof t?d(t)||console.warn(t+" not found"):t,n,r)},xe=function e(t,n,r,i,u,o){var s,a,l,D,f,d,h,g=t.element,m=t.cache,v=t.parent,C=t.x,y=t.y,F=n.width,E=n.height,x=n.scaleX,b=n.scaleY,S=n.rotation,B=n.bounds,A=o&&g.style.cssText,_=o&&g.getBBox&&g.getAttribute("transform"),T=t,k=n.matrix,M=k.e,P=k.f,O=t.bounds.width!==B.width||t.bounds.height!==B.height||t.scaleX!==x||t.scaleY!==b||t.rotation!==S,N=!O&&t.simple&&n.simple&&!u;return N||!v?(x=b=1,S=s=0):(d=(f=function e(t){var n=t._gsap||ne.core.getCache(t);return n.gmCache===ne.ticker.frame?n.gMatrix:(n.gmCache=ne.ticker.frame,n.gMatrix=c(t,!0,!1,!0))}(v)).clone().multiply(n.ctm?n.matrix.clone().multiply(n.ctm):n.matrix),S=p(Math.atan2(d.b,d.a)*De),s=p(Math.atan2(d.c,d.d)*De+S)%360,x=Math.sqrt(Math.pow(d.a,2)+Math.pow(d.b,2)),b=Math.sqrt(Math.pow(d.c,2)+Math.pow(d.d,2))*Math.cos(s*fe),u&&(u=te(u)[0],D=ne.getProperty(u),h=u.getBBox&&"function"==typeof u.getBBox&&u.getBBox(),T={scaleX:D("scaleX"),scaleY:D("scaleY"),width:h?h.width:Math.ceil(parseFloat(D("width","px"))),height:h?h.height:parseFloat(D("height","px"))}),m.rotation=S+"deg",m.skewX=s+"deg"),r?(x*=F!==T.width&&T.width?F/T.width:1,b*=E!==T.height&&T.height?E/T.height:1,m.scaleX=x,m.scaleY=b):(F=oe(F*x/T.scaleX,0),E=oe(E*b/T.scaleY,0),g.style.width=F+"px",g.style.height=E+"px"),i&&w(g,n.props),N||!v?(C+=M-t.matrix.e,y+=P-t.matrix.f):O||v!==n.parent?(m.renderTransform(1,m),d=c(u||g,!1,!1,!0),a=f.apply({x:d.e,y:d.f}),C+=(l=f.apply({x:M,y:P})).x-a.x,y+=l.y-a.y):(f.e=f.f=0,C+=(l=f.apply({x:M-t.matrix.e,y:P-t.matrix.f})).x,y+=l.y),C=oe(C,.02),y=oe(y,.02),!o||o instanceof Ae?(m.x=C+"px",m.y=y+"px",m.renderTransform(1,m)):(g.style.cssText=A,g.getBBox&&g.setAttribute("transform",_||""),m.uncache=1),o&&(o.x=C,o.y=y,o.rotation=S,o.skewX=s,r?(o.scaleX=x,o.scaleY=b):(o.width=F,o.height=E)),o||m},be=[],we="width,height,overflowX,overflowY".split(","),Se=((ae=O.prototype).update=function e(t){var n=this;return this.elementStates=this.targets.map((function(e){return new Ae(e,n.props,n.simple)})),P(this),this.interrupt(t),this.recordInlineStyles(),this},ae.clear=function e(){return this.targets.length=this.elementStates.length=0,P(this),this},ae.fit=function e(t,n,r){for(var i,u,o=v(this.elementStates.slice(0),!1,!0),s=(t||this).idLookup,a=0;a<o.length;a++)i=o[a],r&&(i.matrix=c(i.element,!1,!1,!0)),(u=s[i.id])&&xe(i,u,n,!0,0,i),i.matrix=c(i.element,!1,!1,!0);return this},ae.getProperty=function e(t,n){var r=this.getElementState(t)||de;return(n in r?r:r.props||de)[n]},ae.add=function e(t){for(var n,r,i,u=t.targets.length,o=this.idLookup,s=this.alt;u--;)(i=o[(r=t.elementStates[u]).id])&&(r.element===i.element||s[r.id]&&s[r.id].element===r.element)?(n=this.elementStates.indexOf(r.element===i.element?i:s[r.id]),this.targets.splice(n,1,t.targets[u]),this.elementStates.splice(n,1,r)):(this.targets.push(t.targets[u]),this.elementStates.push(r));return t.interrupted&&(this.interrupted=!0),t.simple||(this.simple=!1),P(this),this},ae.compare=function e(t){function n(e,t,n){return(e.isVisible!==t.isVisible?e.isVisible?g:m:e.isVisible?h:p).push(n)&&v.push(n)}function r(e,t,r){return v.indexOf(r)<0&&n(e,t,r)}var i,u,o,s,a,l,c,D,f=t.idLookup,d=this.idLookup,p=[],h=[],g=[],m=[],v=[],C=t.alt,y=this.alt;for(o in f)a=C[o],l=y[o],s=(i=a?_(t,this,o):f[o]).element,u=d[o],l?(D=u.isVisible||!l.isVisible&&s===u.element?u:l,(c=!a||i.isVisible||a.isVisible||D.element!==a.element?i:a).isVisible&&D.isVisible&&c.element!==D.element?((c.isDifferent(D)?h:p).push(c.element,D.element),v.push(c.element,D.element)):n(c,D,c.element),a&&c.element===a.element&&(a=f[o]),r(c.element!==u.element&&a?a:c,u,u.element),r(a&&a.element===l.element?a:c,l,l.element),a&&r(a,l.element===a.element?l:u,a.element)):(u?u.isDifferent(i)?n(i,u,s):p.push(s):g.push(s),a&&r(a,u,a.element));for(o in d)f[o]||(m.push(d[o].element),y[o]&&m.push(y[o].element));return{changed:h,unchanged:p,enter:g,leave:m}},ae.recordInlineStyles=function e(){for(var t=he[this.props]||me,n=this.elementStates.length;n--;)C(this.elementStates[n],t)},ae.interrupt=function e(t){var n=this,r=[];this.targets.forEach((function(e){var i=e._flip,u=M(i,t?0:1);t&&u&&r.indexOf(i)<0&&i.add((function(){return n.updateVisibility()})),u&&r.push(i)})),!t&&r.length&&this.updateVisibility(),this.interrupted||(this.interrupted=!!r.length)},ae.updateVisibility=function e(){this.elementStates.forEach((function(e){var t=e.element.getBoundingClientRect();e.isVisible=!!(t.width||t.height||t.top||t.left),e.uncache=1}))},ae.getElementState=function e(t){return this.elementStates[this.targets.indexOf(d(t))]},ae.makeAbsolute=function e(){return v(this.elementStates.slice(0),!0,!0).map(E)},O),Be,Ae=((Be=N.prototype).isDifferent=function e(t){var n=this.bounds,r=t.bounds;return n.top!==r.top||n.left!==r.left||n.width!==r.width||n.height!==r.height||!this.matrix.equals(t.matrix)||this.opacity!==t.opacity||this.props&&t.props&&JSON.stringify(this.props)!==JSON.stringify(t.props)},Be.update=function e(t,i){var u=this,s=u.element,a=ne.getProperty(s),l=ne.core.getCache(s),D=s.getBoundingClientRect(),f=s.getBBox&&"function"==typeof s.getBBox&&"svg"!==s.nodeName.toLowerCase()&&s.getBBox(),d=i?new ee(1,0,0,1,D.left+r(),D.top+n()):c(s,!1,!1,!0);u.getProp=a,u.element=s,u.id=function e(t){var n=t.getAttribute("data-flip-id");return n||t.setAttribute("data-flip-id",n="auto-"+le++),n}(s),u.matrix=d,u.cache=l,u.bounds=D,u.isVisible=!!(D.width||D.height||D.left||D.top),u.display=a("display"),u.position=a("position"),u.parent=s.parentNode,u.x=a("x"),u.y=a("y"),u.scaleX=l.scaleX,u.scaleY=l.scaleY,u.rotation=a("rotation"),u.skewX=a("skewX"),u.opacity=a("opacity"),u.width=f?f.width:oe(a("width","px"),.04),u.height=f?f.height:oe(a("height","px"),.04),t&&function e(t,n){for(var r=ne.getProperty(t.element,null,"native"),i=t.props={},u=n.length;u--;)i[n[u]]=(r(n[u])+"").trim();i.zIndex&&(i.zIndex=parseFloat(i.zIndex)||0)}(u,ye[t]||m(t)),u.ctm=s.getCTM&&"svg"===s.nodeName.toLowerCase()&&o(s).inverse(),u.simple=i||1===p(d.a)&&!p(d.b)&&!p(d.c)&&1===p(d.d),u.uncache=0},N),_e,Te=((_e=L.prototype).getStateById=function e(t){for(var n=this.states.length;n--;)if(this.states[n].idLookup[t])return this.states[n]},_e.kill=function e(){this.batch.remove(this)},L),ke,Me=((ke=V.prototype).add=function e(t){var n=this.actions.filter((function(e){return e.vars===t}));return n.length?n[0]:(n=new Te("function"==typeof t?{animate:t}:t,this),this.actions.push(n),n)},ke.remove=function e(t){var n=this.actions.indexOf(t);return 0<=n&&this.actions.splice(n,1),this},ke.getState=function e(t){var n=this,r=re,i=ie;return(re=this).state.clear(),this._kill.length=0,this.actions.forEach((function(e){e.vars.getState&&(e.states.length=0,(ie=e).state=e.vars.getState(e)),t&&e.states.forEach((function(e){return n.state.add(e)}))})),ie=i,re=r,this.killConflicts(),this},ke.animate=function e(){var t,n,r=this,i=re,u=this.timeline,o=this.actions.length;for(re=this,u.clear(),this._abs.length=this._final.length=this._run.length=0,this.actions.forEach((function(e){e.vars.animate&&e.vars.animate(e);var t,n,r=e.vars.onEnter,i=e.vars.onLeave,u=e.targets;u&&u.length&&(r||i)&&(t=new Se,e.states.forEach((function(e){return t.add(e)})),(n=t.compare(Pe.getState(u))).enter.length&&r&&r(n.enter),n.leave.length&&i&&i(n.leave))})),b(this._abs),this._run.forEach((function(e){return e()})),n=u.duration(),t=this._final.slice(0),u.add((function(){n<=u.time()&&(t.forEach((function(e){return e()})),D(r,"onComplete"))})),re=i;o--;)this.actions[o].vars.once&&this.actions[o].kill();return D(this,"onStart"),u.restart(),this},ke.loadState=function e(t){t=t||function e(){return 0};var n=[];return this.actions.forEach((function(e){if(e.vars.loadState){var r,i=function i(u){u&&(e.targets=u),~(r=n.indexOf(i))&&(n.splice(r,1),n.length||t())};n.push(i),e.vars.loadState(i)}})),n.length||t(),this},ke.setState=function e(){return this.actions.forEach((function(e){return e.targets=e.vars.setState&&e.vars.setState(e)})),this},ke.killConflicts=function e(t){return this.state.interrupt(t),this._kill.forEach((function(e){return e.interrupt(t)})),this},ke.run=function e(t,n){var r=this;return this!==re&&(t||this.getState(n),this.loadState((function(){r._killed||(r.setState(),r.animate())}))),this},ke.clear=function e(t){this.state.clear(),t||(this.actions.length=0)},ke.getStateById=function e(t){for(var n,r=this.actions.length;r--;)if(n=this.actions[r].getStateById(t))return n;return this.state.idLookup[t]&&this.state},ke.kill=function e(){this._killed=1,this.clear(),delete ce[this.id]},V),Pe=(R.getState=function e(t,n){var r=A(t,n);return ie&&ie.states.push(r),n&&n.batch&&R.batch(n.batch).state.add(r),r},R.from=function e(t,n){return"clearProps"in(n=n||{})||(n.clearProps=!0),k(t,A(n.targets||t.targets,{props:n.props||t.props,simple:n.simple,kill:!!n.kill}),n,-1)},R.to=function e(t,n){return k(t,A(n.targets||t.targets,{props:n.props||t.props,simple:n.simple,kill:!!n.kill}),n,1)},R.fromTo=function e(t,n,r){return k(t,n,r)},R.fit=function e(t,n,r){var i=r?$(r,Ce):{},u=r||i,o=u.absolute,s=u.scale,a=u.getVars,l=u.props,c=u.runBackwards,D=u.onComplete,f=u.simple,p=r&&r.fitChild&&d(r.fitChild),h=Ee(n,l,f,t),g=Ee(t,0,f,h),m=l?he[l]:me;return l&&w(i,h.props),c&&(C(g,m),"immediateRender"in i||(i.immediateRender=!0),i.onComplete=function(){y(g),D&&D.apply(this,arguments)}),o&&E(g,h),i=xe(g,h,s||p,l,p,i.duration||a?i:0),a?i:i.duration?ne.to(g.element,i):null},R.makeAbsolute=function e(t,n){return(t instanceof Se?t:new Se(t,n)).makeAbsolute()},R.batch=function e(t){return ce[t=t||"default"]||(ce[t]=new Me(t))},R.killFlipsOf=function e(t,n){(t instanceof Se?t.targets:te(t)).forEach((function(e){return e&&M(e._flip,!1!==n?1:2)}))},R.isFlipping=function e(t){var n=R.getByTarget(t);return!!n&&n.isActive()},R.getByTarget=function e(t){return(d(t)||de)._flip},R.getElementState=function e(t,n){return new Ae(d(t),n)},R.convertCoordinates=function e(t,n,r){var i=c(n,!0,!0).multiply(c(t));return r?i.apply(r):i},R.register=function e(n){if(ue="undefined"!=typeof document&&document.body){ne=n,t(ue),te=ne.utils.toArray;var r=ne.utils.snap(.1);oe=function e(t,n){return r(parseFloat(t)+n)}}},R);Pe.version="3.10.4","undefined"!=typeof window&&window.gsap&&window.gsap.registerPlugin(Pe),e.Flip=Pe,e.default=Pe,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default}));