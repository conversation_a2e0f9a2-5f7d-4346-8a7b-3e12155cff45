/*!
* Project name
* Project Version: 1.0
*/
/*eslint-env jquery*/
jQuery(function () {

  if ($(window).width() > 700) {
    $(".super_values .hover-text").hover(function () {

      var hiddenText = $(this).parent().children(".hidden-text");
      hiddenText.toggleClass("show");

    });
  }

  if (navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1) { } else {

    if ($(window).width() > 700) {
      // $(".super_values .hover-text").hover(function () {

      //   var hiddenText = $(this).parent().children(".hidden-text");
      //   hiddenText.toggleClass("show");

      // });

      $('.super_hero_image,.super_hero_title').mousemove(function (event) {
        var xPos = (event.clientX / $(window).width()) - 0.5,
          yPos = (event.clientY / $(window).height()) - 0.5,
          box = $('.super-hero-box'),
          text = $('.content h1');

        TweenLite.to(box, 0.6, {
          rotationY: 5 * xPos,
          rotationX: 5 * yPos,
          ease: Power1.easeOut,
          transformPerspective: 900,
          transformOrigin: 'center'
        });
        TweenLite.to(text, 0.6, {
          rotationY: (-1) * 5 * xPos,
          rotationX: (-1) * 5 * yPos,
          ease: Power1.easeOut,
          transformPerspective: 300,
          transformOrigin: 'center'
        });
      });
    }

  }

});//JQUERY END



gsap.config({ nullTargetWarn: false });



let mouseCursor = document.querySelector(".cursor");
let cursorLinks = document.querySelectorAll('.js-cursor-links');
let yellowArea = document.querySelectorAll('.js-cursor');
let imgFun = document.querySelectorAll('.img-fun');

window.addEventListener("mousemove", cursor);

function cursor(e) {
  mouseCursor.style.top = e.clientY - 43.5 + "px";
  mouseCursor.style.left = e.clientX - 43.5 + "px";
}

cursorLinks.forEach(link => {
  link.addEventListener("mouseleave", () => {
    mouseCursor.classList.remove("link-animate");
    link.classList.remove("hovered-link");
  });

  link.addEventListener("mouseover", () => {
    mouseCursor.classList.add("link-animate");
  });
});

yellowArea.forEach(area => {
  area.addEventListener("mouseleave", () => {
    mouseCursor.classList.remove("cursor--black");
    mouseCursor.classList.remove("cursor--anchor");
  });

  area.addEventListener("mouseover", (e) => {
    mouseCursor.classList.add("cursor--black");
    // console.log(e.target.classList);
  });
});

imgFun.forEach(area => {
  area.addEventListener("mouseleave", () => {
    mouseCursor.classList.remove("inverted");
  });

  area.addEventListener("mouseover", () => {
    mouseCursor.classList.add("inverted");
  });
});



/*--------------------------------
* menu js
--------------------------------*/

const header = document.querySelector("header");
const body = document.querySelector("body");
const hamburger = document.querySelector(".burger-container");
const navLinks = document.querySelector("nav.navbar");
const links = document.querySelectorAll("ul.navbar-nav li");
const svgPaths = document.querySelectorAll(".navbar-brand svg path");

hamburger.addEventListener("click", (e) => {
  body.classList.toggle("no-scroll");
  // header.classList.toggle("bg-yellow");
  navLinks.classList.toggle("menu-opened");
  // links.forEach(link => {
  //   link.classList.toggle("fade");
  // });

  svgPaths.forEach(path => {
    path.classList.toggle("fade");
  });
});



/*--------------------------------
* if media trigger only when it is not mobile
--------------------------------*/
gsap.registerPlugin(ScrollTrigger, ScrollSmoother);

/* --------------------------------
* Fun FOOTER effect
--------------------------------*/
// gsap.registerPlugin(MorphSVGPlugin);

// const down = 'M0-0.3C0-0.3,464,156,1139,156S2278-0.3,2278-0.3V683H0V-0.3z';
// const center = 'M0-0.3C0-0.3,464,0,1139,0s1139-0.3,1139-0.3V683H0V-0.3z';

// ScrollTrigger.create({
//   trigger: '.super_footer_anime',
//   start: 'top bottom',
//   toggleActions: 'play pause resume reverse',
//   // markers: true,
//   onEnter: self => {
//     const velocity = self.getVelocity();
//     const variation = velocity / 10000;

//     gsap.fromTo('#bouncy-path', {
//       morphSVG: down
//     }, {
//       duration: 2,
//       morphSVG: center,
//       ease: `elastic.out(${1 + variation}, ${1 - variation})`,
//       overwrite: 'auto'
//     });
//   }
// });



/*>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
  * if media trigger only when it is not mobile
>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>*/
// if( /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ) {}else{
if (jQuery(window).width() > 992) {


  /*--------------------------------
  * smooth scroll init
  --------------------------------*/
  ScrollSmoother.create({
    smooth: 1.5,
    effects: true,
    normalizeScroll: true,
    ignoreMobileResize: true,
    smoothTouch: 0.1
  });



  /*--------------------------------
  * main title swoop in
  --------------------------------*/

  gsap.set("#super-h1,#super-h2,.super-heading-swoop", { perspective: 400 });

 var mySplitText = new SplitText("#super-h1,#super-h2,.super-heading-swoop", { type: "lines" }),
   lines = mySplitText.lines;


  tl = gsap.timeline().from(lines, {
    duration: 2,
    opacity: 0,
    scale: 1,
    rotationY: -30,
    transformOrigin: "50% 50%",
    ease: "back",
    // stagger: 1,
    onComplete: allDone
  });

  function allDone() {
    mySplitText.revert();
  }

  tl.play(0);

  /* --------------------------------
  * Stagger text in faded
  --------------------------------*/

  // var SplitCheckTitles = new SplitText(".super-checked-reveal", { type: "words,chars" }),
  //   checkedSplit = SplitCheckTitles.chars; //an array of all the divs that wrap each character

  // gsap.from(checkedSplit, {
  //   opacity: 0,
  //   y: 10,
  //   ease: "circ.out",
  //   stagger: 0.009,
  //   scrollTrigger: {
  //     trigger: ".super_checked_titles",
  //     start: "top 60%",
  //     // toggleActions: 'play none none none',
  //     // markers: true
  //   }
  // });


  // var SplitTicketTitles = new SplitText(".super-reveal-ticket", { type: "words,chars" }),
  //   ticketSplit = SplitTicketTitles.chars; //an array of all the divs that wrap each character

  // gsap.from(ticketSplit, {
  //   opacity: 0,
  //   y: 10,
  //   ease: "circ.out",
  //   stagger: 0.009,
  //   scrollTrigger: {
  //     trigger: ".super_ticket",
  //     start: "top 60%",
  //     // toggleActions: 'play none none none',
  //     // markers: true
  //   }
  // });

  // var SplitRevealTitles = new SplitText(".super_reveal_text_stagger", { type: "words,chars" }),
  //   revealtSplit = SplitRevealTitles.chars; //an array of all the divs that wrap each character

  // gsap.from(ticketSplit, {
  //   opacity: 0,
  //   y: 10,
  //   ease: "circ.out",
  //   stagger: 0.02,
  //   scrollTrigger: {
  //     // trigger: ".super_reveal_text_trigger",
  //     start: "top 50%",
  //     // toggleActions: 'play none none none',
  //     // markers: true
  //   }
  // });


  // =============================================
  let fadeElement = document.querySelectorAll(".super_anim_fade");

  if (fadeElement) {
    TweenMax.fromTo(fadeElement, 1.5, { opacity: 0, y:40 }, { opacity: 1,y:0 });
  }

  // =============================================

  let sections = document.querySelectorAll(".super_reveal_text_trigger");

  sections.forEach((element) => {
    let headings = element.querySelectorAll(".super_reveal_text_stagger");

    let tl = gsap.timeline()
      .from(headings, {
        opacity: 0,
        y: 100,
        duration: 1.7,
        ease: "power2.out",
        // stagger: 0.2
      }, 0);

    ScrollTrigger.create({
      trigger: element,
      start: "top 50%",
      // toggleActions: "play none none reverse",
      animation: tl,
      // markers: true
    });

  });



  /*--------------------------------
  * Text on scroll reveal effect
  --------------------------------*/
  // function setupSplits() {

  //   /*--------------------------------
  //   * video title swoop in
  //   --------------------------------*/
  //   gsap.set(".super_big_title_only h2,.super_big_title_only h3", { perspective: 400 });

  //   var SplitBigTitle = new SplitText(".super_big_title_only h2,.super_big_title_only h3", { type: "lines" }),
  //     lines = SplitBigTitle.lines;

  //   gsap.timeline().from(lines, {
  //     scrollTrigger: {
  //       trigger: ".super_big_title_only",
  //       // markers: true,
  //       start: "top 40%",
  //       scrub: 1
  //     },
  //     duration: 1,
  //     opacity: 0,
  //     scale: 1,
  //     rotationY: -30,
  //     transformOrigin: "50% 50%",
  //     ease: "back",
  //     onComplete: allDone
  //   });

  //   function allDone() {
  //     mySplitText.revert();
  //   }

  // };

  // ScrollTrigger.addEventListener("refresh", setupSplits);
  // setupSplits();



  /*--------------------------------
  * portfolio header image move
  --------------------------------*/
  let FollowBox = ".follow-bg-img img";
  let mapWidth = gsap.utils.mapRange(0, document.body.clientWidth, 50, -50)
  let mapHeight = gsap.utils.mapRange(0, document.body.clientHeight, 50, -50)
  let mouseArea = document.querySelector(".header-follow-img");

  if (mouseArea) {
    mouseArea.addEventListener("mousemove", (e) => {
      gsap.to(FollowBox, {
        duration: 3,
        overwrite: "auto",
        x: mapWidth(e.clientX),
        y: mapHeight(e.clientY),
        stagger: 0.1,
        ease: "power4.out",
      });
    });
  }


  /*>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
    * if media trigger only when it is not mobile
  >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>*/
} // if media


// =============================================

/*--------------------------------
* portfolio filter animating and filtering
--------------------------------*/
const allCheckbox = document.querySelector("#all"),
  filters = gsap.utils.toArray(".filter"),
  items = gsap.utils.toArray(".item");

function updateFilters(e) {
  const state = Flip.getState(items), // get the current state
    classes = filters
      .filter((button) => e.target)
      .map((button) => "." + e.target.id),
    matches = classes.length ? gsap.utils.toArray(classes.join(",")) : classes;

  // adjust the display property of each item ("none" for filtered ones, "inline-flex" for matching ones)
  if (e.target.id === "all") {
    items.forEach((item) => (item.style.display = "inline-flex"));
  } else {
    items.forEach(
      (item) =>
      (item.style.display =
        matches.indexOf(item) === -1 ? "none" : "inline-flex")
    );
  }

  // animate from the previous state
  Flip.from(state, {
    duration: 0.4,
    scale: true,
    absolute: true,
    ease: "power1.inOut",
    onEnter: (elements) =>
      gsap.fromTo(
        elements,
        {
          opacity: 0,
          // scale: 0
        },
        {
          opacity: 1,
          // scale: 1,
          duration: 1
        }
      ),
    onLeave: (elements) =>
      gsap.to(elements, {
        opacity: 0,
        // scale: 0,
        duration: 1
      })
  });
}


filters.forEach((btn) => {
  btn.addEventListener("click", function (e) {
    updateFilters(e);
    filters.forEach((btn) => {
      btn.classList.remove("active");
    });
    btn.classList.add("active");
    setTimeout(function () {
      ScrollTrigger.refresh();
      console.log('filter scrolltrigger refresh');
    }, 1000);
  })
});
// =============================================
// allCheckbox.addEventListener("click", (e) => {
//   updateFilters(e);
//   setTimeout(function () {
//     ScrollTrigger.refresh();
//     console.log('all button scrolltrigger refresh');
//   }, 100);
// });



if (document.querySelectorAll(".carousel-box").length) {
  // const wrapper = document.querySelector(".wrapper");
  // const colors = ["#f38630","#6fb936", "#ccc", "#6fb936"];
  const boxes = gsap.utils.toArray(".carousel-box");

  // gsap.set(boxes , {
  // 	backgroundColor: gsap.utils.wrap(colors)
  // });
  const loop = horizontalLoop(boxes, { speed: .5, paused: false, repeat: -1, paddingRight: 20 });

  // boxes.forEach((box, i) => box.addEventListener("click", () => loop.toIndex(i, {duration: 0.8, ease: "power1.inOut"})));

  // document.querySelector(".toggle").addEventListener("click", () => wrapper.classList.toggle("show-overflow"));
  // document.querySelector(".next").addEventListener("click", () => loop.next({duration: 0.4, ease: "power1.inOut"}));
  // document.querySelector(".prev").addEventListener("click", () => loop.previous({duration: 0.4, ease: "power1.inOut"}));


  /*
  This helper function makes a group of elements animate along the x-axis in a seamless, responsive loop.
  
  Features:
   - Uses xPercent so that even if the widths change (like if the window gets resized), it should still work in most cases.
   - When each item animates to the left or right enough, it will loop back to the other side
   - Optionally pass in a config object with values like "speed" (default: 1, which travels at roughly 100 pixels per second), paused (boolean),  repeat, reversed, and paddingRight.
   - The returned timeline will have the following methods added to it:
     - next() - animates to the next element using a timeline.tweenTo() which it returns. You can pass in a vars object to control duration, easing, etc.
     - previous() - animates to the previous element using a timeline.tweenTo() which it returns. You can pass in a vars object to control duration, easing, etc.
     - toIndex() - pass in a zero-based index value of the element that it should animate to, and optionally pass in a vars object to control duration, easing, etc. Always goes in the shortest direction
     - current() - returns the current index (if an animation is in-progress, it reflects the final index)
     - times - an Array of the times on the timeline where each element hits the "starting" spot. There's also a label added accordingly, so "label1" is when the 2nd element reaches the start.
   */
  function horizontalLoop(items, config) {
    items = gsap.utils.toArray(items);
    config = config || {};
    let tl = gsap.timeline({ repeat: config.repeat, paused: config.paused, defaults: { ease: "none" }, onReverseComplete: () => tl.totalTime(tl.rawTime() + tl.duration() * 100) }),
      length = items.length,
      startX = items[0].offsetLeft,
      times = [],
      widths = [],
      xPercents = [],
      curIndex = 0,
      pixelsPerSecond = (config.speed || 1) * 100,
      snap = config.snap === false ? v => v : gsap.utils.snap(config.snap || 1), // some browsers shift by a pixel to accommodate flex layouts, so for example if width is 20% the first element's width might be 242px, and the next 243px, alternating back and forth. So we snap to 5 percentage points to make things look more natural
      totalWidth, curX, distanceToStart, distanceToLoop, item, i;
    gsap.set(items, { // convert "x" to "xPercent" to make things responsive, and populate the widths/xPercents Arrays to make lookups faster.
      xPercent: (i, el) => {
        let w = widths[i] = parseFloat(gsap.getProperty(el, "width", "px"));
        xPercents[i] = snap(parseFloat(gsap.getProperty(el, "x", "px")) / w * 100 + gsap.getProperty(el, "xPercent"));
        return xPercents[i];
      }
    });
    gsap.set(items, { x: 0 });
    totalWidth = items[length - 1].offsetLeft + xPercents[length - 1] / 100 * widths[length - 1] - startX + items[length - 1].offsetWidth * gsap.getProperty(items[length - 1], "scaleX") + (parseFloat(config.paddingRight) || 0);
    for (i = 0; i < length; i++) {
      item = items[i];
      curX = xPercents[i] / 100 * widths[i];
      distanceToStart = item.offsetLeft + curX - startX;
      distanceToLoop = distanceToStart + widths[i] * gsap.getProperty(item, "scaleX");
      tl.to(item, { xPercent: snap((curX - distanceToLoop) / widths[i] * 100), duration: distanceToLoop / pixelsPerSecond }, 0)
        .fromTo(item, { xPercent: snap((curX - distanceToLoop + totalWidth) / widths[i] * 100) }, { xPercent: xPercents[i], duration: (curX - distanceToLoop + totalWidth - curX) / pixelsPerSecond, immediateRender: false }, distanceToLoop / pixelsPerSecond)
        .add("label" + i, distanceToStart / pixelsPerSecond);
      times[i] = distanceToStart / pixelsPerSecond;
    }
    function toIndex(index, vars) {
      vars = vars || {};
      (Math.abs(index - curIndex) > length / 2) && (index += index > curIndex ? -length : length); // always go in the shortest direction
      let newIndex = gsap.utils.wrap(0, length, index),
        time = times[newIndex];
      if (time > tl.time() !== index > curIndex) { // if we're wrapping the timeline's playhead, make the proper adjustments
        vars.modifiers = { time: gsap.utils.wrap(0, tl.duration()) };
        time += tl.duration() * (index > curIndex ? 1 : -1);
      }
      curIndex = newIndex;
      vars.overwrite = true;
      return tl.tweenTo(time, vars);
    }
    tl.next = vars => toIndex(curIndex + 1, vars);
    tl.previous = vars => toIndex(curIndex - 1, vars);
    tl.current = () => curIndex;
    tl.toIndex = (index, vars) => toIndex(index, vars);
    tl.times = times;
    tl.progress(1, true).progress(0, true); // pre-render for performance
    if (config.reversed) {
      tl.vars.onReverseComplete();
      tl.reverse();
    }
    return tl;
  }

}