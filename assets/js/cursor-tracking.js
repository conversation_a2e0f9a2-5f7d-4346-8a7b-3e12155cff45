jQuery(function ($) {

  if (navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1) { } else {
    if ($(window).width() > 700) {

      $('.super_hero_image,.super_hero_title').mousemove(function (event) {
        var xPos = (event.clientX / $(window).width()) - 0.5,
          yPos = (event.clientY / $(window).height()) - 0.5,
          box = $('.super-hero-box'),
          text = $('.content h1');

        TweenLite.to(box, 0.6, {
          rotationY: 5 * xPos,
          rotationX: 5 * yPos,
          ease: Power1.easeOut,
          transformPerspective: 900,
          transformOrigin: 'center'
        });
        TweenLite.to(text, 0.6, {
          rotationY: (-1) * 5 * xPos,
          rotationX: (-1) * 5 * yPos,
          ease: Power1.easeOut,
          transformPerspective: 300,
          transformOrigin: 'center'
        });
      });
    }

  }







});//JQUERY END


  /*--------------------------------
  * portfolio header image move
  --------------------------------*/
  let FollowBox = ".follow-image .wp-block-media-text__media img";
  let mapWidth = gsap.utils.mapRange(0, document.body.clientWidth, 50, -50)
  let mapHeight = gsap.utils.mapRange(0, document.body.clientHeight, 50, -250)
  let mouseArea = document.querySelector(".follow-image");

  if (mouseArea) {
    mouseArea.addEventListener("mousemove", (e) => {
      gsap.to(FollowBox, {
        duration: 3,
        overwrite: "auto",
        x: mapWidth(e.clientX),
        y: mapHeight(e.clientY),
        stagger: 0.1,
        ease: "power4.out",
      });
    });
  }




    // =============================================
  let fadeElement = document.querySelectorAll(".magimanni_fade_in");

  if (fadeElement) {
    TweenMax.fromTo(fadeElement, 1.5, { opacity: 0, y:40 }, { opacity: 1,y:0 });
  }



gsap.set(".magimanni_heading_swoop", { perspective: 400 });

 var mySplitText = new SplitText(".magimanni_heading_swoop", { type: "lines" }),
   lines = mySplitText.lines;


  tl = gsap.timeline().from(lines, {
    duration: 2,
    opacity: 0,
    scale: 1,
    rotationY: -30,
    transformOrigin: "50% 50%",
    ease: "back",
    // stagger: 1,
    onComplete: allDone
  });

  function allDone() {
    mySplitText.revert();
  }

  tl.play(0);


    // =============================================

  let sections = document.querySelectorAll(".reveal_text_trigger");

  sections.forEach((element) => {
    let headings = element.querySelectorAll(".reveal_text_stagger");

    let tl = gsap.timeline()
      .from(headings, {
        opacity: 0,
        y: 100,
        duration: 1.7,
        ease: "power2.out",
        // stagger: 0.2
      }, 0);

    ScrollTrigger.create({
      trigger: element,
      start: "top 50%",
      // toggleActions: "play none none reverse",
      animation: tl,
      // markers: true
    });

  });