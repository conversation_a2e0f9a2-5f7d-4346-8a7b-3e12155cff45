section.super-team-header {
    .title-content {
        // margin: 80px 0 180px;
        margin: 15% 0 8%;

        h1 {
            @include bisonbold250;
            color: $main-yellow;
            text-transform: uppercase;
            text-align: right;

            span {
                color: black;
                font-size: 70px;
                line-height: 60px;
                display: inline-block;
                max-width: 50%;
                vertical-align: middle;
            }

        }
    }

    .description {
        margin-left: auto;
        padding-right: 5%;
        max-width: 500px;

        p {
            font-family: $montserrat;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 28px;
            color: rgb(0, 0, 0);
        }
    }

    @media(max-width:991px) {
        .title-content {
            margin-top: 182px;
            padding: 0 5%;

            h1 {
                text-align: left;

                span {
                    font-size: 30px;
                    line-height: 30px;
                    display: block;
                    margin-bottom: 15px;
                }
            }
        }

        .description {
            padding: 50px 5%;
            background: linear-gradient(180deg, rgba(235, 235, 235, 0) 0%, #E4E4E4 100%);

            p {
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}


section.super-team-float {
    padding: 183px 0 200px 0;

    .card-container {
        display: flex;
        flex-wrap: wrap;
    }

    .card-wrapper {
        flex: 1 0 26%;
        position: relative;

        @media (max-width: 991.98px) {
            flex-basis: 34%;
        }

        &:nth-child(1) {
            text-align: center;
        }

        &:nth-child(2) {
            text-align: center;
            margin-top: 15%;
        }

        &:nth-child(3) {
            text-align: end;
            margin-top: 8%;
        }

        &:nth-child(4) {
            margin-top: -8%;
        }

        &:nth-child(5) {
            margin-top: 9%;
        }

        &:nth-child(6) {
            margin-top: 5%;
        }

        &:nth-child(7) {
            text-align: end;
            margin-top: 8%;
        }

        &:nth-child(8) {
            text-align: center;
            margin-top: 15%;
        }

        &:nth-child(9) {
            text-align: end;
            margin-top: 4%;
        }


        @media(max-width:991.98px) {
            &:nth-child(1) {
                text-align: left;
            }

            &:nth-child(2) {
                margin-top: 20%;
                text-align: left;
            }

            &:nth-child(3) {
                text-align: left;
                margin-top: -10%;
            }

            &:nth-child(4) {
                text-align: left;
                margin-top: 20%;
            }

            &:nth-child(5) {
                text-align: left;
                margin-top: -10%;
            }

            &:nth-child(6) {
                text-align: left;
                margin-top: 20%;
            }

            &:nth-child(7) {
                text-align: left;
                margin-top: -10%;
            }

            &:nth-child(8) {
                text-align: left;
                margin-top: 20%;
            }

            &:nth-child(9) {
                text-align: start;
                margin-top: -10%;

                .card {
                    max-width: 50%;
                }
            }

        }
    }

    .card {
        padding: 10px;

        .titles {
            position: absolute;
            top: 0;
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: space-between;
            padding: 18px 28px;

            h4 {
                font-family: $bison;
                font-style: normal;
                font-weight: 300;
                font-size: 30px;
                line-height: 42px;
                letter-spacing: 0.03em;
                text-transform: uppercase;
                color: #FCC64F;
                text-align: left;
            }

            p {
                font-family: $montserrat;
                font-style: normal;
                font-weight: 500;
                font-size: 18px;
                line-height: 28px;
                color: white;
                text-align: left;
            }

            @media (max-width: 991.98px) {
                h4 {
                    font-size: 22px;
                    line-height: 24px;
                }

                p {
                    font-size: 14px;
                    line-height: 16px;
                }
            }
        }
    }


    @media (max-width: 991.98px) {
        padding: 113px 0 113px 0;

        .card {
            padding: 5px;
        }
    }
}


section.super-team-bg-text {
    background: url('../images/team/experts-bg.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    min-height: 100vh;
    height: 100%;
    display: flex;
    align-items: flex-end;

    .title-content {
        padding: 100px;

        h2 {
            font-family: $bison;
            font-size: 110px;
            line-height: 85px;
            color: $main-yellow;

            span {
                color: white;
            }
        }
    }

    @media(max-width:991.98px) {
        .title-content {
            padding: 34px 20px;

            h2 {
                font-size: 60px;
                line-height: 52px;
            }
        }
    }
}

section.super-team-members {
    background-color: $dark;
    padding: 183px 0 200px 0;

    .card-container {
        display: flex;
        flex-wrap: wrap;
    }

    .card-wrapper {
        flex: 1 0 19%;
        position: relative;

        @media (max-width: 1199.98px) {
            flex-basis: 21%;
        }

        @media (max-width: 991.98px) {
            flex-basis: 34%;
        }
    }

    .content{
        position:relative;
        .overlay{
            position: absolute;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(53, 53, 63, 0.5);
            mix-blend-mode: normal;
            opacity: 0.5;
        }
    }

    .card {
        padding: 10px;

        .titles {
            position: absolute;
            top: 0;
            display: flex;
            flex-direction: column;
            height: 100%;
            justify-content: space-between;
            padding: 18px 28px;

            h4 {
                font-family: $bison;
                font-style: normal;
                font-weight: 300;
                font-size: 30px;
                line-height: 42px;
                letter-spacing: 0.03em;
                text-transform: uppercase;
                color: #FCC64F;
            }

            p {
                font-family: $montserrat;
                font-style: normal;
                font-weight: 500;
                font-size: 18px;
                line-height: 28px;
                color: white;
            }

            @media (max-width: 991.98px) {
                h4 {
                    font-size: 22px;
                    line-height: 24px;
                }

                p {
                    font-size: 14px;
                    line-height: 16px;
                }
            }
        }
    }

    @media(max-width:991.98px) {
        padding: 70px 0;

        .card {
            padding: 5px;
        }
    }
}

section.super-projects-backed {
    display: flex;
    align-items: center;
    min-height: 100vh;
    text-align: center;
    background: #EEEDEF;

    .title {
        h3 {
            font-family: $bison;
            font-size: 50px;
            line-height: 50px;
            color: $orange;

            br{
                display: none;
            }
            @media(max-width:991.98px) {
                font-size: 30px;
                line-height: 30px;
                br{
                    display:block;
                }
            }
        }

        margin-bottom:70px;
    }

    .logos-wrap {
        display: flex;
        justify-content: center;

        .logo {
            padding: 31px;
        }
        @media (max-width:1070px) {
            flex-wrap: wrap;
        }
    }


    @media (max-width:991.98px) {
        .logos-wrap {
            .logo {
                flex-basis: 50%;
            }
        }
    }
}