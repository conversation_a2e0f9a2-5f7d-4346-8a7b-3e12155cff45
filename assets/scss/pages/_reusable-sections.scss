// -----------------------------------------------------------------------------
// This file contains styles that are for not page specific
// -----------------------------------------------------------------------------
section.super_hero_title {

    .container-second {
        max-width: 1680px;
        margin-left: 7.4%;
        margin: auto;

        @media(max-width:991.98px) {
            padding: 5%;
        }
    }

    .content {
        margin-bottom: -215px;
        z-index: 1;
        margin-top: 90px;

        @media(max-width: 768px) {
            margin-bottom: -162px;
        }

        h1 {
            @include bisonbold250;
            color: #fff;
            text-align: center;
            text-transform: uppercase;
        }

        h2 {
            font-family: $bison;
            font-style: normal;
            font-weight: 700;
            font-size: 50px;
            margin-bottom: 13px;

            @media(max-width: 768px) {
                font-size: 30px;
            }
        }

        h3 {
            font-family: $montserrat;
            font-style: normal;
            font-weight: 600;
            font-size: 18px;
            line-height: 20px;
            text-transform: uppercase;
            color: white;
            margin-top: 47px;

            @media(max-width: 768px) {
                font-size: 14px;
            }
        }

        ul.logos {
            list-style: none;
            display: flex;
            flex-direction: row;
            justify-content: center;
            padding: 0;
            // margin-top: 20px;

            li.logo {
                margin: 0 25px;
            }
        }
    }

    .wrapper {
        // height: 20%;
        width: 33%;
        // background: #555;
        position: relative;
        display: flex;
        align-items: center;
        overflow: hidden;
        margin: auto;
        margin-top: 20px;

        @media(max-width: 768px) {
            width: 50%;
        }

        @media(max-width: 420px) {
            width: 90%;
        }

    }

    .carousel-box {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100px;
        height: auto;
        margin: 0;
        padding: 0;
        position: relative;
        flex-shrink: 0;
        margin: 0 20px;

        img {
            max-height: 40px;
        }
    }

}

section.super_hero_image {

    img {
        filter: brightness(0.9);

        @media(max-width: 768px) {
            height: 520px;
            object-fit: cover;
        }

    }
}


section.super_checked_titles {
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-top: 223px;
    padding-bottom: 351px;

    .title {
        p {
            font-family: $montserrat;
            font-size: 18px;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: 0em;
            text-align: center;

        }

        h2 {
            font-family: $bison;
            font-size: 110px;
            font-weight: 400;
            line-height: 85px;
            letter-spacing: 0em;
            text-align: center;

        }

        h3 {
            font-family: $bison;
            font-size: 50px;
            font-weight: 700;
            line-height: 50px;
            letter-spacing: 0em;
            text-align: center;
            color: $main-yellow;
        }

        .mobile_br {
            display: none;
        }
    }

    .title-margin {
        margin: 7.9% 0;
    }

    @media(max-width: 768px) {
        padding: 80px 0 130px;
        height: auto;

        .title {
            h2 {
                font-size: 60px;
                line-height: 52px;
                display: flex;
                flex-direction: column;
                align-items: center;
            }

            i.correct {
                margin-top: 15px;
            }
        }

        .title-margin {
            margin: 98px 0;
        }
    }

}

section.super_ticket {
    .bg-yellow {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }

    .title-wrap {
        display: flex;
        flex-wrap: wrap;
        // justify-content: space-between;
        justify-content: space-evenly;
    }

    .title {
        h2 {
            font-family: $bison;
            font-size: 110px;
            font-weight: 700;
            line-height: 85px;
            letter-spacing: 0em;
            color: white;
        }

        h3 {
            font-family: $bison;
            font-size: 50px;
            font-weight: 700;
            line-height: 50px;
            letter-spacing: 0em;
        }

        p {
            font-family: $montserrat;
            font-size: 18px;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: 0em;
        }

        &-main {
            text-align: center;
        }

        &-first {
            text-align: left;
            padding-left: 70px;
            margin-bottom: 80px
        }

        &-second {
            text-align: center;
            padding: 0 30px;
            margin-bottom: 80px
        }

        &-third {
            text-align: right;
            padding-right: 70px;
        }
    }

    .title-margin {
        margin-bottom: 120px;
    }

    // @media(max-width: 1360px) {
    //     .title {
    //         &-first {
    //             padding-left: 4%;
    //         }

    //         &-second {}

    //         &-third {
    //             padding-right: 4%;
    //         }
    //     }

    // }

    @media(max-width: 1284px) {
        .title {
            &-third {
                // margin-top: 5%;
            }
        }

        .title-wrap {
            justify-content: space-evenly;
        }
    }

    @media(max-width: 813px) {

        .bg-yellow {
            height: auto;
            padding-bottom: 112px;
            padding-top: 100px;
        }

        .title {
            width: 100%;

            h2 {
                font-size: 60px;
                line-height: 52px;
            }

            h3 {
                font-size: 30px;
                line-height: 30px;
                margin-bottom: 15px;
            }

            p {
                font-size: 14px;
                line-height: 16px;
            }

            &-first {
                text-align: center;
                padding-left: 0;

                br {
                    display: none;
                }
            }

            &-second {
                text-align: center;
                margin: 63px 0;
            }

            &-third {
                text-align: center;
                padding-right: 0;
                margin-top: 0;

                br {
                    display: none;
                }
            }

            .title-margin {
                margin-bottom: 66px;
            }
        }

    }
}




section.super_big_title_only {
    .bg-super-orange {
        padding-top: 160px;
        min-height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .super_content {
        z-index: 1;
    }

    h2 {
        @include bisonbold250;
        color: #fff;
        margin-top: 25px;
    }

    h3 {
        font-family: $bison;
        font-size: 50px;
        font-weight: 400;
        line-height: 50px;
        letter-spacing: 0em;
        text-align: center;
    }

    @media(max-width: 768px) {
        .bg-super-orange {
            padding-top: 90px;
            padding-bottom: 150px;
            min-height: auto;
            align-items: flex-start;
        }

        h3 {
            font-size: 30px;
            line-height: 30px;
        }

        h2 {
            text-align: center;
        }
    }
}




section.super_image_text {
    .super_body_image {
        padding: 0 30px;
        margin-top: -287px;
        text-align: center;

        img {
            filter: brightness(0.8);
        }

        video {
            width: 100%;
            height: auto;
        }

        .container_video {
            position: relative;
        }

        .video_overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(53, 53, 63, 0.5);
        }

        .video-desktop {
            display: block
        }

        .video-mobile {
            display: none
        }

        @media (max-width: 768px) {
            .video-desktop {
                display: none
            }
        }

        @media (max-width: 768px) {
            .video-mobile {
                display: block
            }
        }

    }

    .super_content {
        padding: 130px;

        p {
            font-family: $montserrat;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 28px;
        }
    }

    .super_team_link {
        text-align: right;

        a {
            font-family: $bison;
            font-style: normal;
            font-weight: 700;
            font-size: 50px;
            line-height: 50px;
            letter-spacing: 0.03em;
            text-transform: uppercase;
            color: $orange;
            display: inline-block;
            text-decoration: none;

            // &:after {
            //     content: '';
            //     display: flex;
            //     background-color: $orange;
            //     height: 6px;
            //     width: 100%;
            //     margin-top: 13px;
            // }
        }
    }

    @media(max-width: 768px) {
        .super_body_image {
            padding: 0 20px;
            margin-top: -230px;
            text-align: center;

            img {
                filter: brightness(0.8);
                height: 540px;
                object-fit: cover;
            }
        }

        .super_team_link {
            text-align: center;
            margin: 65px 0 74px;

            a {
                font-size: 30px;
                line-height: 50px;
            }
        }

        .super_content {
            padding: 40px 19px;
        }
    }
}

section.super_quote {
    .bg-yellow {
        min-height: 100vh;
        padding: 0 7%;
        padding-top: 7%;
        padding-bottom: 7%;
    }

    .super_content {
        // height: 52vh;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;

        h3 {
            font-family: $bison;
            font-size: 70px;
            font-weight: 700;
            line-height: 70px;
            letter-spacing: 0em;
            text-align: center;
            color: white;
        }

        p {
            font-family: $montserrat;
            font-size: 18px;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: 0em;
            text-align: center;
        }

        .title-margin {
            margin-bottom: 200px;
            margin-top: 100px;
        }
    }


    @media(max-width: 768px) {
        .bg-yellow {
            padding: 75px 20px 94px;
            min-height: auto;
        }

        svg {
            width: 77px;
            height: 69px;
            margin-left: 48px;
        }

        .super_content {
            height: auto;

            h3 {
                font-size: 50px;
                line-height: 50px;
            }

            p {
                font-weight: 600;
                font-size: 14px;
                line-height: 16px;
            }

            .title-margin {
                margin-top: 43px;
                margin-bottom: 67px;
            }
        }
    }
}

section.super_values {

    .bg-super-dark {
        min-height: 100vh;
        display: flex;
        justify-content: center;
        flex-direction: column;
        padding: 7% 7%;
    }

    .small-text {
        margin-bottom: 90px;

        p {
            font-family: $montserrat;
            font-size: 18px;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: 0em;
            color: white;
        }

        @media(min-width: 1680px) {
            padding-right: 5%;

            p {
                text-align: right;
            }
        }
    }

    .wrap {
        margin-bottom: 50px;
    }

    .hover-text {
        @media(min-width: 1680px) {
            display: flex;
            justify-content: flex-end;
            padding-right: 5%;
        }

        h2 {
            font-family: $bison;
            font-size: 70px;
            font-weight: 400;
            line-height: 70px;
            letter-spacing: 0.03em;
            text-align: left;
            color: white;
        }
    }

    .hidden-text {
        opacity: 0;

        p {
            font-family: $montserrat;
            font-size: 18px;
            font-weight: 500;
            line-height: 28px;
            letter-spacing: 0em;
            text-align: left;
            color: white;

        }

        &.show {
            animation: fadeIn 350ms ease-in;
            opacity: 1;
        }
    }

    // Animation to fade in 
    @keyframes fadeIn {
        from {
            opacity: 0;
        }

        to {
            opacity: 1;
        }
    }


    @media(max-width: 768px) {
        .bg-super-dark {
            height: auto;
            padding: 112px 7% 89px;
        }

        .small-text {
            margin-bottom: 43px;

            p {
                font-size: 14px;
                line-height: 16px;
                text-align: center;
            }
        }

        .hidden-text {
            opacity: 1;

            p {
                font-size: 14px;
                line-height: 22px;
                text-align: center;
            }
        }

        .hover-text {
            h2 {
                text-align: center;
                font-size: 50px;
                line-height: 50px;
            }
        }
    }
}


section.super_portfoolio {
    display: flex;
    align-items: center;
    padding: 7%;

    .content_margin {
        margin-top: 5%;
        margin-bottom: 4%;
    }

    .main-title {
        margin-bottom: 150px;

        h3 {
            font-family: $bison;
            font-size: 50px;
            font-weight: 400;
            line-height: 50px;
            letter-spacing: 0em;
            text-align: center;
        }

        h2 {
            color: $orange;
            @include bisonbold250;
            line-height: 194px;
            text-align: center;
        }
    }

    .super_content {
        p {
            font-family: $montserrat;
            font-size: 18px;
            font-weight: 600;
            line-height: 28px;
            letter-spacing: 0em;
            text-align: left;
        }

        h2 {
            font-family: $bison;
            font-size: 70px;
            font-weight: 700;
            line-height: 60px;
            letter-spacing: 0.03em;
        }

        a {
            font-family: $bison;
            font-size: 30px;
            font-weight: 700;
            line-height: 42px;
            letter-spacing: 0.03em;
            text-transform: uppercase;
            color: $orange;
            display: inline-block;
            text-decoration: none;
        }
    }


    .super_team_link {
        display: flex;
        justify-content: flex-end;
        align-items: flex-end;

        a {
            font-family: $bison;
            font-style: normal;
            font-weight: 700;
            font-size: 50px;
            line-height: 50px;
            letter-spacing: 0.03em;
            text-transform: uppercase;
            color: $orange;
            display: inline-block;
            text-decoration: none;

            &:after {
                content: '';
                display: flex;
                background-color: $orange;
                height: 6px;
                width: 100%;
                margin-top: 13px;
            }
        }
    }

    @media(max-width: 768px) {
        padding: 112px 20px 96px;

        .content_margin {
            margin-top: 53px;
            margin-bottom: 53px;
        }

        .main-title {
            margin-bottom: 50px;

            h3 {
                font-size: 30px;
                line-height: 30px;
                text-align: center;
            }

            h2 {
                text-align: center;
            }
        }

        .super_content {
            p {
                font-size: 14px;
                line-height: 16px;
                text-align: center;
            }

            h2 {
                font-size: 50px;
                line-height: 50px;
                text-align: center;
            }

            a {
                font-size: 20px;
                line-height: 44px;
                text-align: center;
                display: block;
            }
        }

        .super_team_link {
            justify-content: center;
            margin: 40px 0 0;

            a {
                font-size: 30px;
                line-height: 50px;
            }
        }

    }

    @media (max-width: 374.98px) {
        padding: 112px 0 96px;

        .super_content {
            h2 {
                font-size: 40px;
                line-height: 40px;
            }
        }

    }
}



section.super-privacy-article {
    padding: 7%;

    article {
        color: $dark;

        h2 {
            font-family: $bison;
            font-style: normal;
            font-weight: 300;
            font-size: 70px;
            line-height: 60px;
            letter-spacing: 0.03em;
            text-transform: uppercase;

            margin-bottom: 20px;
            margin-top: 160px;

            &:first-of-type {
                margin-top: 0;
            }
        }

        h3 {
            font-family: $bison;
            font-style: normal;
            font-weight: 300;
            font-size: 30px;
            line-height: 34px;

            margin-top: 48px;
            margin-bottom: 10px;
        }

        p,
        ol,
        table,
        .row {
            font-family: $montserrat;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 28px;
        }

        ol {
            padding-left: 50px;
        }

        .column {
            margin: 10px 0;
        }

        table {
            margin: 30px 0;
            width: 100%;

            th,
            td {
                padding: 2px 10px;
                vertical-align: top;
            }
        }

    }
}