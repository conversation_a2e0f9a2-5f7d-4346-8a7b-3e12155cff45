section.super-why-header {
    .title-content {
        // margin: 80px 0 180px;
        margin: 10% 0 8%;

        .title {
            padding: 0 5%;
            padding-right: 15%;

            br {
                display: none;
            }
        }

        h1 {
            font-family: $bison;
            font-size: 110px;
            line-height: 85px;
            color: black;
            text-transform: uppercase;

            span {
                color: $orange;
            }

            @media screen and (min-width: 375px) {
                font-size: 60px;
                line-height: 56px;
            }

            @media screen and (max-width: 375px) {
                font-size: 60px;
                line-height: 56px;
            }

            @media screen and (min-width: 1000px) {
                font-size: 6vw;
                line-height: 5vw;
            }

            @media(min-width: 2560px) {
                font-size: 151px;
                line-height: 126px;
            }
        }
    }

    .description {
        padding: 0 5%;
        margin-top: 75px;
        padding-right: 25%;

        p {
            font-family: $montserrat;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 28px;
            color: rgb(0, 0, 0);
        }
    }


    @media(max-width:991px) {
        .title-content {
            margin-top: 100px;
            padding: 0;
            margin-bottom: 0;

            .title {
                br {
                    display: block;
                }
            }

            h1 {
                text-align: left;
            }
        }

        .description {
            padding: 50px 5%;
            background: linear-gradient(180deg, rgba(235, 235, 235, 0) 0%, #E4E4E4 100%);
            margin-top: 50%;

            p {
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}

section.super-why-text-columns {
    background-color: $orange;
    padding: 10% 7%;

    .row {
        &:nth-child(even) {
            .title-content{
                margin: 10% 0 10% auto;
            }
        }
    }

    .title-content{
        h2{
            font-family: $bison;
            font-size: 70px;
            line-height: 60px;
            color: white;
            margin-bottom: 40px;
        }
        p{
            font-family: $montserrat;
            font-weight: 500;
            font-size: 18px;
            line-height: 28px;
            color: white;
        }
    }

    @media(max-width: 991.98px) {
        .title-content{
            margin: 20% auto;
            h2{
                font-size: 50px;
                line-height: 50px;
            }
            p{
                font-size: 14px;
                line-height: 22px;
            }
        }
    }
}