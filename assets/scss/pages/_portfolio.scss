.follow-bg-img {
    position: relative;
    background-color: #EEEDEF;
    position: absolute;
    z-index: -1;
    top: 50px;

    img {
        left: -10%;
        width: 120%;
        height: 100%;
        position: relative;
        top: -100px;

        @media(max-width: 768px) {
            margin-top: 0;
            width: 100%;
            left: 0;
        }
    }

    @media (max-width:1200px) {
        display: none;
    }
}


.super-portfolio-header {
    // padding: 0 0 205px 85px;
    padding: 0 75px 205px;

    .title-content {
        // margin: 80px 0 180px;
        margin: 5% 0 8%;

        h4 {
            font-size: 50px;
            font-family: $bison;
            font-style: normal;
            font-weight: 300;
            font-size: 50px;
            color: white;
            text-transform: uppercase;
        }

        h1 {
            @include bisonbold250;
            color: white;
            text-transform: uppercase;

            span {
                color: $main-yellow;
            }
        }
    }

    .description {
        p {
            font-family: $montserrat;
            font-style: normal;
            font-weight: 500;
            font-size: 18px;
            line-height: 28px;
            color: white;
        }
    }


    @media (max-width:991.98px) {
        padding: 0 10px 205px;

        .title-content {
            margin: 100px 0 10%;

            h4 {
                font-size: 30px;
            }
        }
    }
}


section.super-portfolio-companys {

    background-color: #EEEDEF;
    min-height: 100vh;
    padding: 0 85px;
    position: relative;
    z-index: 0;

    .containor-fluid {
        position: relative;
        top: -200px;
    }

    .row-hide-over {
        overflow: hidden;
    }

    .col-scroll-x {
        overflow-x: auto;
    }

    .buttons-container {
        display: inline-flex;
        text-align: left;
        margin-bottom: 55px;

        button {
            font-family: $bison;
            font-style: normal;
            font-weight: 300;
            font-size: 30px;
            line-height: 42px;
            border: none;
            color: white;
            background: none;
            margin-right: 37px;

            &.active {
                color: $main-yellow;
            }
        }
    }

    .flex {
        display: flex;
        // flex-wrap: wrap;
        justify-content: center;
        white-space: nowrap;
    }

    .item {
        width: 18%;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        font-size: 24px;
        color: rgb(0, 0, 0);
        margin: 8px;
        position: relative;

        .content {
            background-color: white;
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            transition: 0.5s;

            img {
                height: 100%;
                width: 100%;
            }
            
            a{
                height: 100%;
                -webkit-transition: all 0.5s ease;
                transition: all 0.5s ease;
            }
        }

        &::after {
            content: "";
            display: block;
            padding-bottom: 100%;
        }

        .hover-el {
            display: block;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            transition-property: transform;
            z-index: -1;
            transition: 0.5s;
        }

        &:hover {
            .hover-el {
                background-color: $main-yellow;
                transform: rotate(-3.45deg) scale(1.1);
                z-index: 1;
            }

            .content {
                box-shadow: 0 2px 20px rgba(0, 0, 0, .2);
                transform: scaleY(1.2);
                z-index: 2;

                a {
                    height: 84.34%;
                    width: 100%;
                }
            }
        }
    }

    @media screen and (max-width: 1150px) {
        .item {
            width: calc(33.3% - 20px);
        }
    }

    @media screen and (max-width: 768px) {
        padding: 0 10px;

        .row {
            width: 100%;
            margin: 0;
        }

        .item {
            width: calc(50% - 20px);
        }

        .boxes-container {
            text-align: center;
        }
        
        .buttons-container{
            margin-bottom: 30px;
            button {
                font-weight: 300;
                font-size: 22px;
                line-height: 42px;
                letter-spacing: 0.03em;
                margin-right: 10px;
            }
        }

        // .buttons-container {
        //     button {
        //         margin-right: 0;
        //         ;
        //     }
        // }
    }

}