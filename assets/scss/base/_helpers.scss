// -----------------------------------------------------------------------------
// This file contains CSS helper classes.
// -----------------------------------------------------------------------------
.pt-100 {
    padding-top: 100px;
}

.pb-100 {
    padding-bottom: 100px;

}

.pb-200 {
    padding-bottom: 200px;
}

.pt-200 {
    padding-top: 200px;
}

.backtop {
    bottom: 68pt;
    padding: 0;
    position: fixed;
    right: 20pt;
    top: auto;
    display: none;

    @media (max-width: 768px) {
        display: inline;
    }
}

.bg-yellow {
    background-color: $main-yellow;
}

.bg-super-dark {
    background-color: $dark;
}

.bg-super-orange{
    background-color: $orange;
}

i {
    &.correct {
        display: inline-block;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='84' height='84' fill='none'%3E%3Cpath fill='%23FCC64F' d='M42 81.375a39.375 39.375 0 1 1 0-78.75 39.375 39.375 0 0 1 0 78.75Zm0-73.5a34.125 34.125 0 1 0 0 68.25 34.125 34.125 0 0 0 0-68.25Z'/%3E%3Cpath fill='%23FCC64F' d='M35.878 57.75a2.624 2.624 0 0 1-1.916-.84L21.703 43.785a2.624 2.624 0 0 1 3.833-3.57l10.342 11.051L58.453 27.09a2.626 2.626 0 1 1 3.833 3.57L37.794 56.91a2.623 2.623 0 0 1-1.916.84Z'/%3E%3C/svg%3E");
        height: 78px;
        width: 78px;
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
    }
}

.relative {
    position: relative;
}

hr.font-table {
    height: 1px;
    opacity: 1;
    margin: 4px 0;
}

.font {
    &.montserrat {
        font-family: $montserrat !important;

        &.w-400 {
            font-weight: 400;
        }

        &.w-500 {
            font-weight: 500;
        }

        &.size-9 {
            font-size: 9px;
        }
    }

    &.roboto {
        font-family: $roboto !important;

        &.w-300 {
            font-weight: 300;
        }

        &.w-400 {
            font-weight: 400;
        }

        &.w-500 {
            font-weight: 500;
        }

        &.w-700 {
            font-weight: 700;
        }

        &.size-10 {
            font-size: 10px;
        }

        &.size-8 {
            font-size: 8px;
        }
    }
}

@media(min-width:768px){
    .mobile_only{
        display: none;
    }
}
.no-scroll{
    overflow: hidden!important;
}