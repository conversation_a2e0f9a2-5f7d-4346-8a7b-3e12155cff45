// -----------------------------------------------------------------------------
// This file contains all styles related to the button component.
// -----------------------------------------------------------------------------
.btn {
    transition: 0.25s ease;
    outline: 0;
&:focus{
    outline:0;
    box-shadow:none;
}
    &.btn-cvi-download {
        position: relative;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: black;
        border: 1px solid black;
        border-radius: 0;
        width: 110px;
        height: 36px;
        padding: 0;
        margin: 0;
        margin-left: 25px;
        transition: ease-in .3s;
        text-transform: uppercase;

        &::after {
            content: '';
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 96 58'%3E%3Cpath fill='%23FF3D60' d='M4.744 9.58 54.59 0l36.408 5.827L96 52.758 59.775 58 0 54.59 4.744 9.58Z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-size: contain;
            background-position: 0 -50%;
            width: 89px;
            height: 52px;
            z-index: -1;
            position: absolute;
            left: -25px;
            // transition: ease-in .3s;
            transition: all 0.85s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }


        &::before{
            transition: all 0.85s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            content: '';
            height: 100%;
            background: black;
            position: absolute;
            top: 0;
            left: 0;
            z-index:-1;
            width: 0%;
			background: #f53d60;
        }
		&:hover{
			&::before{
			background: #f53d60;
				width: 100%;
			}
            &:after{
                height: 100%;
            }
		}	

    }
}