.link {
    position: relative;
    --border-height: 5px;
    --border-color: #EE7944;
    
    &::before,
    &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: var(--border-height);
        background: var(--border-color);
        top: 100%;
        left: 0;
        pointer-events: none;
    }

    &--super--line {
        &::before {
            transform-origin: 0% 50%;
            transform: scale3d(0, 1, 1);
            transition: transform 0.3s;
        }

        &:hover::before {
            transform: scale3d(1, 1, 1);
        }

        &::after {
            content: '';
            top: calc(100% + 4px);
            transition: transform 0.3s;
            transform-origin: 100% 50%;
        }

        &:hover::after {
            transform: scale3d(0, 1, 1);
        }
    }


}