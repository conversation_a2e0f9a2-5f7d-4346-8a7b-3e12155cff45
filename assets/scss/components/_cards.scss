.card {
    // width: 40%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    // font-size: 24px;
    // color: rgb(0, 0, 0);
    // margin: 8px;
    position: relative;

    // .content {
    //     background-color: white;
    //     position: absolute;
    //     width: 100%;
    //     height: 100%;
    //     transition: 0.5s;
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;

    //     img {
    //         // height: 100%;
    //         // width: 100%;
    //         -webkit-transition: all 0.5s ease;
    //     }
    // }

    // &::after {
    //     content: "";
    //     display: block;
    //     padding-bottom: 100%;
    // }

    .hover-el {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transition: 0.5s;
        transition-property: transform;
        z-index: -1;
    }

    &:hover {
        .hover-el {
            background-color: $orange;
            transform: rotate(-3.45deg) scaleX(1.1) scaleY(0.95);
            z-index: 1;
        }

        .content {
            box-shadow: 0 2px 20px rgba(0, 0, 0, .2);
            // transform: scaleY(1.2);
            z-index: 2;

            img {
                // height: 84.34%;
                // width: 100%;
            }
        }
    }
}