/*Variables*/
:root {
   --yellow-color: #FCC64F;
   --green-rbg-color: 0, 212, 113;
}

.cursor {
   width: 87px;
   height: 87px;
   border: 5px solid var(--yellow-color);
   border-radius: 50%;
   position: fixed;
   animation: cursor-animate 950ms infinite alternate;
   pointer-events: none;
   transition: all ease .3s;
   transition-property:background-color, transform, border;
   z-index: 2;
   @media screen and (max-width:992px){
      display: none;
   }
}

// .navbar:hover~.cursor {
//    border: 5px solid var(--black-color);
// }


/*Modificador*/
.cursor--expand {
   animation: cursor-animate-3 550ms forwards;
   border: 10px solid var(--yellow-color);
}

.cursor--expand::after {
   border: 15px solid rgba(var(--green-rbg-color), .3);
}


.cursor--black {
   border: 5px solid black;
}

.link-animate {
   background-color: black;
   animation: cursor-animate-2 550ms forwards;
   cursor:none !important;
   border: 5px solid black;
}

.hovered-link {
   a {
      color: white !important;
      z-index: 3;
      position: relative;
   }
}

.inverted{
   animation:none;
   backdrop-filter: invert(1);
   transform: scale(3);
}

/*Keyframes*/
@keyframes cursor-animate {
   from {
      transform: scale(1);
   }

   to {
      transform: scale(1.1)
   }
}

@keyframes cursor-animate-2 {
   from {
      transform: scale(1);
   }

   to {
      transform: scale(0.3);
   }
}

@keyframes cursor-animate-3 {
   0% {
      transform: scale(1);
   }

   50% {
      transform: scale(3);
   }

   100% {
      transform: scale(1);
      opacity: 0;
   }
}