// -----------------------------------------------------------------------------
// This file contains all styles related to the footer of the site/application.
// -----------------------------------------------------------------------------
footer.super_footer {
    position: relative;

    #footer-img {
        height: 100%;
        width: 100%;
        display: block;
        overflow: visible;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: -1;
    }

    .container-fluid {
        // padding: 80px 40px 50px;
        padding: 5% 2.5% 3%;
        background-color: $dark;
    }

    .title_content {
        margin-bottom: 150px;

        h3 {
            font-family: $bison;
            font-size: 70px;
            font-weight: 400;
            line-height: 60px;
            letter-spacing: 0em;
            text-align: left;
            color: white;
        }


        .big_link {
            text-align: right;

            a {
                display: inline-block;
                cursor: none;

                // &::after {
                //     content: '';
                //     display: block;
                //     position: relative;
                //     height: 25px;
                //     background-color: $main-yellow;
                //     margin-top: 15px;
                //     transition: ease .4s;
                //     width: 100%;
                // }

                // &:hover::after {
                //     background: transparent;
                // }
            }

            h2 {
                pointer-events: none;
                display: inline-block;
                @include bisonbold250;
                color: $main-yellow;
            }
        }
    }

    .info_content {
        .info_wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-end;

            .email,
            .address {
                p {
                    font-family: $montserrat;
                    font-size: 18px;
                    font-weight: 500;
                    line-height: 28px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: white;
                }

                h3 {
                    font-family: $bison;
                    font-size: 30px;
                    font-weight: 700;
                    line-height: 42px;
                    letter-spacing: 0.03em;
                    text-align: left;
                    color: $main-yellow;
                }

                a {
                    text-decoration: none;
                }
            }
        }
    }

    .footer_bottom {
        margin-top: 60px;

        .nav {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: flex-end;

            // .logo {}

            .footer_links {
                list-style-type: none;
                display: flex;
                flex-direction: row;
                margin-bottom:0;

                li {
                    margin: 0 40px;

                    a {
                        color: white;
                        text-decoration: none;
                        text-transform: uppercase;
                        font-family: $montserrat;
                        font-style: normal;
                        font-weight: 600;
                        font-size: 12px;
                    }
                }
            }
        }
    }



    @media(max-width: 992px) {
        svg#footer-img {
            display: none;
        }

        .container-fluid {
            padding: 85px 20px 20px;
            background-color: $dark;
        }


        .title_content {
            margin-bottom: 90px;

            h3 {
                font-size: 50px;
                line-height: 50px;
                text-align: center;

                br {
                    display: none;
                }
            }


            .big_link {
                text-align: center;
                margin: 72px 0 0;

                a {
                    display: inline-block;

                    &::after {
                        height: 10px;
                        margin-top: 5px;
                    }
                }

                h2 {
                    font-size: 100px;
                    line-height: 80px;

                    br {
                        display: none;
                    }
                }
            }
        }


        .info_content {
            .info_wrap {
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .email,
                .address {
                    p {
                        text-align: center;
                        font-size: 14px;
                        line-height: 16px;
                    }

                    h3 {
                        text-align: center;
                        font-size: 20px;
                        line-height: 20px;
                    }

                    margin-bottom:44px;
                }

                .social {
                    a {
                        padding: 0 5px;
                    }
                }

                .email {
                    a {
                        text-decoration: underline;
                    }
                }


            }
        }

        .footer_bottom {
            margin-top: 90px;

            .nav {
                flex-direction: column;
                justify-content: center;
                align-items: center;

                .logo {
                    margin-bottom: 30px;

                    svg {
                        width: 100%;
                        height: 17px;
                    }
                }

                .footer_links {
                    list-style-type: none;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;
                    padding: 0;
                    width: 100%;


                    li {
                        margin: 0;

                        a {
                            font-size: 10px;
                            line-height: 50px;
                        }
                    }
                }
            }
        }


    }
}