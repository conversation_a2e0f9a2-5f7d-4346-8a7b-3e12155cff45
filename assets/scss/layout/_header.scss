// -----------------------------------------------------------------------------
// This file contains all styles related to the header of the site/application.
// -----------------------------------------------------------------------------
$cubic: cubic-bezier(0.4, 0.01, 0.165, 0.99);
$menuItems: 4;

// .header-container {
//     // max-width: 1680px;
// }

header {
    padding-bottom: 80px;
}

// Animation to fade in 
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

nav.navbar {
    padding: 0;

    .container-fluid {
        padding: 0;
    }

    a.navbar-brand {
        padding: 0;
        margin: 0;
        position: relative;
        margin-left: 30px;
    }


    ul.navbar-nav {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-top: 10px;
        padding-bottom: 22px;

        @media (min-width:992px) and (max-width: 1440px) {
            flex-wrap: wrap;
        }

        li.nav-item {
            position: static;
            margin: 0 20px 0;
            padding: 20px 0 10px;
            min-width: fit-content;
            text-transform: uppercase;


            a.nav-link {
                position: relative;
                color: black;
                padding: 0 0 0;
                transition: ease .2s;
                font-weight: 600;
                font-family: 'Montserrat';
                font-style: normal;
                font-size: 12px;
                cursor: none;
                //     transition: transform 0.3s cubic-bezier(0.2, 1, 0.8, 1); 
                // :hover {
                //     transform: translate3d(0, -2px, 0);
                // }

                &::after {
                    display: none;
                }

                &::before {
                    content: '';
                    position: absolute;
                    width: 100%;
                    height: 1px;
                    background: currentColor;
                    top: 100%;
                    left: 0;
                    pointer-events: none;

                    transform-origin: 50% 100%;
                    transition: clip-path 0.3s, transform 0.3s cubic-bezier(0.2, 1, 0.8, 1);
                    clip-path: polygon(0% 0%, 0% 100%, 0 100%, 0 0, 100% 0, 100% 100%, 0 100%, 0 100%, 100% 100%, 100% 0%);
                }

                @media(min-width: 992px) {
                    &:hover::before {
                        transform: translate3d(0, 2px, 0) scale3d(1.08, 3, 1);
                        clip-path: polygon(0% 0%, 0% 100%, 50% 100%, 50% 0, 50% 0, 50% 100%, 50% 100%, 0 100%, 100% 100%, 100% 0%);
                    }
                }


                
                // &.active {
                //     // font-weight: bold;
                // }
            }

            // &:hover .nav-link {
            //     // border-bottom: 1px solid;
            // }
        }
    }

    .mobile-menu-social {
        margin: auto;
        height: 0;
        margin-top: 80px;
        opacity: 0;
        display: none;

        a {
            margin: 0 5px;
        }
    }
}



@media screen and (max-width: 991px) {
    nav.navbar a.navbar-brand {
        margin-left: 15px;
    }

    .navbar-brand svg {
        z-index: 4;
        position: relative;
        width: 126px;
        height: 18px;

        path {

            &:nth-child(1) {
                transition: all 0.5s ease 0.1s;
            }

            &:nth-child(2) {
                transition: all 0.5s ease 0.2s;
            }

            &:nth-child(3) {
                transition: all 0.5s ease 0.3s;
            }

            &:nth-child(4) {
                transition: all 0.5s ease 0.4s;
            }

            &:nth-child(5) {
                transition: all 0.5s ease 0.5s;
            }

            &:nth-child(6) {
                transition: all 0.5s ease 0.6s;
            }

            &:nth-child(7) {
                transition: all 0.5s ease 0.7s;
            }

            &:nth-child(8) {
                transition: all 0.5s ease 0.8s;
            }

            &:nth-child(9) {
                transition: all 0.5s ease 0.9s;
            }

            &:nth-child(10) {
                transition: all 0.5s ease 1s;
            }

            &:nth-child(11) {
                transition: all 0.5s ease 1.1s;
            }

            &.fade {
                fill: white !important;

                &:nth-child(1) {
                    fill: $main-yellow !important;
                }
            }

        }
    }


    nav.navbar {
        height: 70px;
        // background-color: $main-yellow;
        position: absolute;
        display: block;
        top: 0;
        left: 0;
        width: 100%;
        transition: all 0.5s ease-out, background 0s ease-out;
        transition-delay: 0.2s;
        z-index: 12;

        .container-fluid,
        ul.navbar-nav {
            height: 0;
        }

        

        .burger-container {
            position: relative;
            display: inline-block;
            height: 35px;
            width: 50px;
            cursor: pointer;
            transform: rotate(0deg);
            transition: all 0.3s $cubic;
            user-select: none;
            -webkit-tap-highlight-color: transparent;

            #hamburger {
                width: 28px;
                height: 10px;
                position: relative;
                display: block;
                margin: -4px auto 0;
                top: 50%;

                .bar {
                    width: 100%;
                    height: 2px;
                    display: block;
                    position: relative;
                    background: #1F1F27;
                    transition: all 0.3s $cubic;
                    transition-delay: 0s;

                    &.topBar {
                        transform: translateY(0px) rotate(0deg);
                    }

                    &.btmBar {
                        transform: translateY(6px) rotate(0deg);
                    }
                }
            }
        }


        ul.navbar-nav {
            position: relative;
            display: block;
            padding: 0px 48px 0;
            list-style: none;
            width: 100%;
            text-align: center;
            margin-top: 50px;

            li.nav-item {
                margin-top: 5px;
                transform: scale(1.15) translateY(-30px);
                opacity: 0;
                margin: 0 0 37px;
                width: 80%;
                margin-left: auto;
                margin-right: auto;
                // transition: transform 0.5s $cubic, opacity 0.6s $cubic;

                // @for $i from 1 through $menuItems {
                //     &:nth-child(#{$i}) {
                //         transition-delay: #{0.56 - ($i * 0.07)}s;
                //     }
                // }

                a.nav-link {
                    display: block;
                    position: relative;
                    color: #FFF;
                    width: 100%;
                    font-size: 16px;
                    pointer-events: none;
                }
            }
        }

        &.menu-opened {
            height: 100%;
            background-color: #1F1F27;
            transition: all 0.3s ease-in, background 0.5s ease-in;
            transition-delay: 0.25s;
            overflow: hidden;

            .container-fluid ul.navbar-nav {
                height: initial;
            }

            .container-fluid{
                height:inherit;
                align-items: unset;
                padding-top:10px;
            }

            .mobile-menu-social {
                transition: all 0.3s ease-in;
                transition-delay: 0.8s;
                // height: 100%;
                opacity: 1;
                margin-top: 0;
            }

            .mobile-menu-social {
                display: block;
            }

            .burger-container {
                transform: rotate(90deg);

                #hamburger {
                    .bar {
                        transition: all 0.4s $cubic;
                        transition-delay: 0.2s;
                        background: $main-yellow;

                        &.topBar {
                            transform: translateY(4px) rotate(45deg);
                        }

                        &.btmBar {
                            transform: translateY(3px) rotate(-45deg);
                        }
                    }
                }
            }

            ul.navbar-nav {
                margin-top:0;
                li.nav-item {
                    transition: transform 0.5s $cubic, opacity 0.6s $cubic;
                    transform: scale(1) translateY(0px);
                    opacity: 1;

                    a.nav-link {
                        color: white;
                        pointer-events: auto;
                    }

                    @for $i from 1 through $menuItems {
                        &:nth-child(#{$i}) {
                            transition-delay: #{0.07 * $i+0.2}s;
                        }
                    }
                }
            }
        }


    }
}