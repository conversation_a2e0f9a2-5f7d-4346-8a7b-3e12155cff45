header.portfolio-bg-img {
    @media (max-width:1200px) {
        background: url('../images/portfolio/port_head_img.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
    }

    ul.navbar-nav {
        li.nav-item {
            a.nav-link {
                color: rgb(255, 255, 255);
            }
        }
    }

    .navbar-brand svg {
        path {
            fill: white;
        }
    }

    nav.navbar .burger-container #hamburger .bar {
        background-color: white;
    }
}


header.team-header {
    position: relative;

    @media (max-width:1200px) {
        background: url('../images/team/header-bg.jpg');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 90%;
    }

    .follow-bg-img {
        background-color: transparent;
        height: 100%;
        width: 100%;
        overflow: hidden;
        top: 0;

        img {
            width: 120%;
            height: auto;
            top: -100px;
        }
    }

    @media(max-width:991px) {
        padding: 0;
    }
}

header.why-header {
    padding-bottom: 0;

    @media (min-width:992px) {
        position: relative;
    }

    @media (max-width:1200px) {
        background: url('../images/whyus/header-bg.png');
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
    }

    .follow-bg-img {
        position: absolute;
        z-index: -1;
        top: 0;
        right: 0;
        width: 50%;
        overflow: hidden;
        height: 100%;

        img {
            left: -10%;
            width: 120%;
            height: auto;
            position: relative;
            top: -100px;
        }
    }
}


.super-privacy-header {
    padding: 10% 7% 5%;
    
    h1 {
        font-family: $bison;
        font-style: normal;
        font-weight: 300;
        font-size: 110px;
        line-height: 85px;
        text-transform: uppercase;
        color: $dark;
    }
}