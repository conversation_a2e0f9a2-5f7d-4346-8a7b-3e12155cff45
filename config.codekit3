{"AAInfo": "This is a CodeKit 3 project config file. EDITING THIS FILE IS A POOR LIFE DECISION. Doing so may cause CodeKit to crash and/or corrupt your project. Several critical values in this file are 64-bit integers, which JavaScript JSON parsers do not support because JavaScript cannot handle 64-bit integers. These values will be corrupted if the file is parsed with JavaScript. This file is not backwards-compatible with CodeKit 1 or 2. For details, see https://codekitapp.com/", "buildSteps": [{"name": "Process All Remaining Files and Folders", "stepType": 1, "uuidString": "2624B465-D9F8-43BB-8770-CFA439F27812"}], "creatorBuild": "34264", "files": {"/.editorconfig": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/.editorconfig", "oF": 0}, "/.htaccess": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/.htaccess", "oF": 0}, "/assets/css/theme.min.css": {"aP": 1, "bl": 0, "ci": 0, "co": 0, "ft": 16, "ma": 0, "oA": 0, "oAP": "/assets/css/theme.min-min.css", "oF": 0, "pg": 0}, "/assets/css/theme.min.css.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/assets/css/theme.min.css.map", "oF": 0}, "/assets/fonts/BisonBold/font.woff": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/assets/fonts/BisonBold/font.woff", "oF": 0}, "/assets/fonts/BisonBold/font.woff2": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/assets/fonts/BisonBold/font.woff2", "oF": 0}, "/assets/images/body_image.jpg": {"ft": 16384, "iS": 421235, "jF": 0, "oA": 0, "oAP": "/assets/images/body_image.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/bolt.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/bolt.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/check-icon.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/check-icon.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/Ellipse 7.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/Ellipse 7.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/fecebook.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/fecebook.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/front-logos/bobw.png": {"ft": 32768, "iS": 53000, "oA": 0, "oAP": "/assets/images/front-logos/bobw.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/bobw.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/front-logos/bobw.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/front-logos/bolt.png": {"ft": 32768, "iS": 21983, "oA": 0, "oAP": "/assets/images/front-logos/bolt.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/koos.png": {"ft": 32768, "iS": 53668, "oA": 0, "oAP": "/assets/images/front-logos/koos.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/koos.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/front-logos/koos.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/front-logos/montonio.png": {"ft": 32768, "iS": 32222, "oA": 0, "oAP": "/assets/images/front-logos/montonio.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/montonio.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/front-logos/montonio.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/front-logos/salv.png": {"ft": 32768, "iS": 58237, "oA": 0, "oAP": "/assets/images/front-logos/salv.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/salv.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/front-logos/salv.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/front-logos/snack.png": {"ft": 32768, "iS": 26775, "oA": 0, "oAP": "/assets/images/front-logos/snack.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/sonar.png": {"ft": 32768, "iS": 69119, "oA": 0, "oAP": "/assets/images/front-logos/sonar.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/sonar.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/front-logos/sonar.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/front-logos/starship.png": {"ft": 32768, "iS": 47975, "oA": 0, "oAP": "/assets/images/front-logos/starship.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/veriff.png": {"ft": 32768, "iS": 43364, "oA": 0, "oAP": "/assets/images/front-logos/veriff.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/front-logos/veriff.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/front-logos/veriff.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/header_image.jpg": {"ft": 16384, "iS": 244409, "jF": 0, "oA": 0, "oAP": "/assets/images/header_image.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/header_image1.jpg": {"ft": 16384, "iS": 848789, "jF": 0, "oA": 0, "oAP": "/assets/images/header_image1.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/linked_in.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/linked_in.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/monese.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/monese.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/portfolio/07092021_11 1.jpg": {"ft": 16384, "iS": 412099, "jF": 0, "oA": 0, "oAP": "/assets/images/portfolio/07092021_11 1.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/3.png": {"ft": 32768, "iS": 12668, "oA": 0, "oAP": "/assets/images/portfolio/logos/3.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/4.png": {"ft": 32768, "iS": 10087, "oA": 0, "oAP": "/assets/images/portfolio/logos/4.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/5.png": {"ft": 32768, "iS": 11464, "oA": 0, "oAP": "/assets/images/portfolio/logos/5.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/6.png": {"ft": 32768, "iS": 20949, "oA": 0, "oAP": "/assets/images/portfolio/logos/6.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/7.png": {"ft": 32768, "iS": 21696, "oA": 0, "oAP": "/assets/images/portfolio/logos/7.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/8.png": {"ft": 32768, "iS": 13934, "oA": 0, "oAP": "/assets/images/portfolio/logos/8.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/9.png": {"ft": 32768, "iS": 19341, "oA": 0, "oAP": "/assets/images/portfolio/logos/9.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/10.png": {"ft": 32768, "iS": 22023, "oA": 0, "oAP": "/assets/images/portfolio/logos/10.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/11.png": {"ft": 32768, "iS": 14094, "oA": 0, "oAP": "/assets/images/portfolio/logos/11.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/12.png": {"ft": 32768, "iS": 19652, "oA": 0, "oAP": "/assets/images/portfolio/logos/12.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/13.png": {"ft": 32768, "iS": 15086, "oA": 0, "oAP": "/assets/images/portfolio/logos/13.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/14.png": {"ft": 32768, "iS": 15124, "oA": 0, "oAP": "/assets/images/portfolio/logos/14.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/15.png": {"ft": 32768, "iS": 22902, "oA": 0, "oAP": "/assets/images/portfolio/logos/15.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/16.png": {"ft": 32768, "iS": 17140, "oA": 0, "oAP": "/assets/images/portfolio/logos/16.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/17.png": {"ft": 32768, "iS": 15103, "oA": 0, "oAP": "/assets/images/portfolio/logos/17.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/18.png": {"ft": 32768, "iS": 12480, "oA": 0, "oAP": "/assets/images/portfolio/logos/18.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/19.png": {"ft": 32768, "iS": 6808, "oA": 0, "oAP": "/assets/images/portfolio/logos/19.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/20.png": {"ft": 32768, "iS": 22609, "oA": 0, "oAP": "/assets/images/portfolio/logos/20.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/21.png": {"ft": 32768, "iS": 17916, "oA": 0, "oAP": "/assets/images/portfolio/logos/21.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/22.png": {"ft": 32768, "iS": 22790, "oA": 0, "oAP": "/assets/images/portfolio/logos/22.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/23.png": {"ft": 32768, "iS": 27329, "oA": 0, "oAP": "/assets/images/portfolio/logos/23.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/24.png": {"ft": 32768, "iS": 14323, "oA": 0, "oAP": "/assets/images/portfolio/logos/24.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/25.png": {"ft": 32768, "iS": 14738, "oA": 0, "oAP": "/assets/images/portfolio/logos/25.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/26.png": {"ft": 32768, "iS": 17391, "oA": 0, "oAP": "/assets/images/portfolio/logos/26.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/27.png": {"ft": 32768, "iS": 30396, "oA": 0, "oAP": "/assets/images/portfolio/logos/27.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/28.png": {"ft": 32768, "iS": 16457, "oA": 0, "oAP": "/assets/images/portfolio/logos/28.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/29.png": {"ft": 32768, "iS": 17917, "oA": 0, "oAP": "/assets/images/portfolio/logos/29.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/30.png": {"ft": 32768, "iS": 14934, "oA": 0, "oAP": "/assets/images/portfolio/logos/30.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/31.png": {"ft": 32768, "iS": 16156, "oA": 0, "oAP": "/assets/images/portfolio/logos/31.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/32.png": {"ft": 32768, "iS": 10877, "oA": 0, "oAP": "/assets/images/portfolio/logos/32.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/33.png": {"ft": 32768, "iS": 13210, "oA": 0, "oAP": "/assets/images/portfolio/logos/33.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/34.png": {"ft": 32768, "iS": 19915, "oA": 0, "oAP": "/assets/images/portfolio/logos/34.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/35.png": {"ft": 32768, "iS": 16922, "oA": 0, "oAP": "/assets/images/portfolio/logos/35.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/36.png": {"ft": 32768, "iS": 24729, "oA": 0, "oAP": "/assets/images/portfolio/logos/36.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/37.png": {"ft": 32768, "iS": 26152, "oA": 0, "oAP": "/assets/images/portfolio/logos/37.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/38.png": {"ft": 32768, "iS": 15603, "oA": 0, "oAP": "/assets/images/portfolio/logos/38.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/39.png": {"ft": 32768, "iS": 23948, "oA": 0, "oAP": "/assets/images/portfolio/logos/39.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/40.png": {"ft": 32768, "iS": 19752, "oA": 0, "oAP": "/assets/images/portfolio/logos/40.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/41.png": {"ft": 32768, "iS": 19195, "oA": 0, "oAP": "/assets/images/portfolio/logos/41.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/42.png": {"ft": 32768, "iS": 19256, "oA": 0, "oAP": "/assets/images/portfolio/logos/42.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/43.png": {"ft": 32768, "iS": 73265, "oA": 0, "oAP": "/assets/images/portfolio/logos/43.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/44.png": {"ft": 32768, "iS": 44369, "oA": 0, "oAP": "/assets/images/portfolio/logos/44.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/45.png": {"ft": 32768, "iS": 19677, "oA": 0, "oAP": "/assets/images/portfolio/logos/45.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/46.png": {"ft": 32768, "iS": 17259, "oA": 0, "oAP": "/assets/images/portfolio/logos/46.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/47.png": {"ft": 32768, "iS": 7466, "oA": 0, "oAP": "/assets/images/portfolio/logos/47.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/logos/48.png": {"ft": 32768, "iS": 10583, "oA": 0, "oAP": "/assets/images/portfolio/logos/48.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/portfolio/port_head_img.jpg": {"ft": 16384, "iS": 1301667, "jF": 0, "oA": 0, "oAP": "/assets/images/portfolio/port_head_img.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/quote.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/quote.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/starship.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/starship.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/superangel-favicon.png": {"ft": 32768, "iS": 4209, "oA": 0, "oAP": "/assets/images/superangel-favicon.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/superangel-logo 1.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/superangel-logo 1.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/team/backed-logos/heatep-siht.png": {"ft": 32768, "iS": 7602, "oA": 0, "oAP": "/assets/images/team/backed-logos/heatep-siht.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/backed-logos/igavene.png": {"ft": 32768, "iS": 8158, "oA": 0, "oAP": "/assets/images/team/backed-logos/igavene.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/backed-logos/investeerimisfest.png": {"ft": 32768, "iS": 8988, "oA": 0, "oAP": "/assets/images/team/backed-logos/investeerimisfest.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/backed-logos/kood-johvi.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/team/backed-logos/kood-johvi.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/team/backed-logos/rekett.svg": {"ft": 2097152, "miP": 0, "oA": 2, "oAP": "/assets/images/team/backed-logos/rekett.svg", "oF": 0, "opt": 0, "plM": 3758088159, "prP": 0}, "/assets/images/team/backed-logos/ykssarvik.png": {"ft": 32768, "iS": 5375, "oA": 0, "oAP": "/assets/images/team/backed-logos/ykssarvik.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Diana.png": {"ft": 32768, "iS": 170567, "oA": 0, "oAP": "/assets/images/team/Diana.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/experts-bg.png": {"ft": 32768, "iS": 1424676, "oA": 0, "oAP": "/assets/images/team/experts-bg.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/header-bg.jpg": {"ft": 16384, "iS": 684778, "jF": 0, "oA": 0, "oAP": "/assets/images/team/header-bg.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Kaari.png": {"ft": 32768, "iS": 145778, "oA": 0, "oAP": "/assets/images/team/Kaari.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Kairi.png": {"ft": 32768, "iS": 178031, "oA": 0, "oAP": "/assets/images/team/Kairi.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Kalev.png": {"ft": 32768, "iS": 163199, "oA": 0, "oAP": "/assets/images/team/Kalev.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Marek.jpg": {"ft": 16384, "iS": 112155, "jF": 0, "oA": 0, "oAP": "/assets/images/team/Marek.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Marko.png": {"ft": 32768, "iS": 150707, "oA": 0, "oAP": "/assets/images/team/Marko.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-1.jpg": {"ft": 16384, "iS": 215138, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-1.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-2.jpg": {"ft": 16384, "iS": 122986, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-2.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-3.jpg": {"ft": 16384, "iS": 177399, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-3.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-4.jpg": {"ft": 16384, "iS": 113687, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-4.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-5.jpg": {"ft": 16384, "iS": 215985, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-5.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-6.jpg": {"ft": 16384, "iS": 109835, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-6.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-7.jpg": {"ft": 16384, "iS": 109620, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-7.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-8.jpg": {"ft": 16384, "iS": 109628, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-8.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-9.jpg": {"ft": 16384, "iS": 120132, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-9.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/members/team-10.jpg": {"ft": 16384, "iS": 110602, "jF": 0, "oA": 0, "oAP": "/assets/images/team/members/team-10.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Mikko.png": {"ft": 32768, "iS": 183749, "oA": 0, "oAP": "/assets/images/team/Mikko.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Rain.png": {"ft": 32768, "iS": 192095, "oA": 0, "oAP": "/assets/images/team/Rain.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/team/Veljo.png": {"ft": 32768, "iS": 176974, "oA": 0, "oAP": "/assets/images/team/Veljo.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/whyus/header-bg.png": {"ft": 32768, "iS": 467112, "oA": 0, "oAP": "/assets/images/whyus/header-bg.png", "oF": 0, "oIPL": 0, "opt": 0, "oT": 1, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/images/whyus/why-section.jpg": {"ft": 16384, "iS": 759933, "jF": 0, "oA": 0, "oAP": "/assets/images/whyus/why-section.jpg", "oF": 0, "oIPL": 0, "opt": 0, "ou": "lpckwebp-none", "q": 100, "rq": 75}, "/assets/js/theme.js": {"bF": 0, "ft": 64, "ma": 1, "mi": 1, "oA": 0, "oAP": "/assets/js/theme.min.js", "oF": 2, "sC": 0, "tS": 0}, "/assets/js/theme.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/assets/js/theme.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/theme.min.js.map": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/assets/js/theme.min.js.map", "oF": 0}, "/assets/js/vendors.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/assets/js/vendors.min.js", "oF": 2, "sC": 3, "tS": 0}, "/assets/js/vendors.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/assets/js/vendors.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/bootstrap.bundle.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/assets/js/vendors/bootstrap.bundle.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/Flip.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/assets/js/vendors/Flip.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/gsap.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/assets/js/vendors/gsap.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/MorphSVGPlugin.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 0, "oAP": "/assets/js/vendors/MorphSVGPlugin.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/ScrollSmoother.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/assets/js/vendors/ScrollSmoother.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/ScrollTrigger.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/assets/js/vendors/ScrollTrigger.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/js/vendors/SplitText.min.js": {"bF": 0, "ft": 64, "ma": 0, "mi": 1, "oA": 1, "oAP": "/assets/js/vendors/SplitText.min-min.js", "oF": 0, "sC": 3, "tS": 0}, "/assets/scss/abstracts/_functions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/abstracts/_functions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/abstracts/_mixins.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/abstracts/_mixins.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/abstracts/_variables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/abstracts/_variables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/base/_base.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/base/_base.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/base/_fonts.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/base/_fonts.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/base/_helpers.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/base/_helpers.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/base/_typography.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/base/_typography.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/components/_button.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/components/_button.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/components/_cards.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/components/_cards.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/components/_cursor.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/components/_cursor.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/components/_links.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/components/_links.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/layout/_footer.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/layout/_footer.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/layout/_header.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/layout/_header.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/layout/_section-headers.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/layout/_section-headers.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/pages/_portfolio.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/pages/_portfolio.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/pages/_reusable-sections.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/pages/_reusable-sections.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/pages/_team.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/pages/_team.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/pages/_why.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/pages/_why.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/theme.scss": {"aP": 1, "bl": 1, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 1, "oA": 0, "oAP": "/assets/css/theme.min.css", "oF": 2, "oS": 1, "pg": 0, "sct": 1}, "/assets/scss/themes/_default.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/themes/_default.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_accordion.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_accordion.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_alert.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_alert.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_badge.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_badge.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_breadcrumb.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_breadcrumb.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_button-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_button-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_buttons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_buttons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_card.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_card.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_carousel.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_carousel.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_close.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_close.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_containers.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_containers.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_dropdown.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_dropdown.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_forms.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_forms.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_functions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_functions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_grid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_grid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_helpers.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_helpers.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_images.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_images.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_list-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_list-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_mixins.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_mixins.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_modal.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_modal.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_nav.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_nav.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_navbar.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_navbar.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_offcanvas.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_offcanvas.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_pagination.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_pagination.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_placeholders.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_placeholders.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_popover.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_popover.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_progress.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_progress.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_reboot.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_reboot.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_root.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_root.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_spinners.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_spinners.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_tables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_tables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_toasts.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_toasts.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_tooltip.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_tooltip.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_transitions.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_transitions.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_type.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_type.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_utilities.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_utilities.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/_variables.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/_variables.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/bootstrap-grid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/assets/scss/vendors/bootstrap/bootstrap-grid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/bootstrap-reboot.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/assets/scss/vendors/bootstrap/bootstrap-reboot.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/bootstrap-utilities.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 0, "oAP": "/assets/scss/vendors/bootstrap/bootstrap-utilities.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/bootstrap.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/bootstrap.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_floating-labels.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_floating-labels.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_form-check.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_form-check.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_form-control.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_form-control.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_form-range.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_form-range.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_form-select.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_form-select.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_form-text.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_form-text.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_input-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_input-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_labels.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_labels.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/forms/_validation.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/forms/_validation.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_clearfix.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_clearfix.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_colored-links.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_colored-links.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_position.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_position.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_ratio.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_ratio.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_stacks.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_stacks.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_stretched-link.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_stretched-link.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_text-truncation.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_text-truncation.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_visually-hidden.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_visually-hidden.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/helpers/_vr.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/helpers/_vr.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_alert.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_alert.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_backdrop.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_backdrop.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_border-radius.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_border-radius.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_box-shadow.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_box-shadow.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_breakpoints.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_breakpoints.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_buttons.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_buttons.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_caret.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_caret.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_clearfix.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_clearfix.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_color-scheme.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_color-scheme.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_container.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_container.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_deprecate.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_deprecate.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_forms.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_forms.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_gradients.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_gradients.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_grid.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_grid.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_image.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_image.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_list-group.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_list-group.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_lists.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_lists.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_pagination.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_pagination.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_reset-text.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_reset-text.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_resize.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_resize.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_table-variants.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_table-variants.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_text-truncate.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_text-truncate.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_transition.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_transition.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_utilities.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_utilities.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/mixins/_visually-hidden.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/mixins/_visually-hidden.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/utilities/_api.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/utilities/_api.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/scss/vendors/bootstrap/vendor/_rfs.scss": {"aP": 0, "bl": 0, "co": 0, "dP": 10, "ec": 1, "ft": 4, "ma": 0, "oA": 1, "oAP": "/assets/scss/vendors/bootstrap/vendor/_rfs.css", "oF": 0, "oS": 0, "pg": 0, "sct": 1}, "/assets/video/super-sky-gif.gif": {"ft": 4194304, "iS": 18419734, "oA": 0, "oAP": "/assets/video/super-sky-gif.gif", "oF": 0, "opt": 0, "ou": "lpckwebp-none", "rq": 75}, "/assets/video/super-sky-video.mp4": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/assets/video/super-sky-video.mp4", "oF": 0}, "/basecamp.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/basecamp.php", "oF": 0}, "/esg-policy.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/esg-policy.php", "oF": 0}, "/footer.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/footer.php", "oF": 0}, "/header.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/header.php", "oF": 0}, "/index.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/index.php", "oF": 0}, "/portfolio.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/portfolio.php", "oF": 0}, "/privacy-policy.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/privacy-policy.php", "oF": 0}, "/robots.txt": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/robots.txt", "oF": 0}, "/team.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/team.php", "oF": 0}, "/test.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/test.php", "oF": 0}, "/why.php": {"cB": 0, "ft": 8192, "hM": 0, "oA": 0, "oAP": "/why.php", "oF": 0}}, "hooks": [], "manualImportLinks": {}, "projectAttributes": {"creationDate": 558266542, "displayValue": "superangel", "displayValueWasSetByUser": 0, "iconImageName": "meme-soon", "iconImageWasSetByUser": 0}, "projectSettings": {"abortBuildOnError": 1, "allowInjectionReloads": 0, "alwaysUseExternalServer": 1, "animateCSSInjections": 0, "autoBuildNewItems": 1, "autoprefixerEnableIEGrid": 0, "babel7PresetType": 1, "babelAllowRCFiles": 0, "babelAuxiliaryCommentAfter": "", "babelAuxiliaryCommentBefore": "", "babelConfigType": 0, "babelCustomPluginsList": "", "babelCustomPresetsList": "", "babelExcludeString": "/\\/node_modules\\//, /\\/core-js\\//, /\\/bower_components\\//", "babelInsertModuleIDs": 0, "babelModuleID": "", "babelNoComments": 0, "babelPlugins": {"arrow-functions": {"active": 0}, "async-generator-functions": {"active": 0}, "async-to-generator": {"active": 0}, "block-scoped-functions": {"active": 0}, "block-scoping": {"active": 0}, "class-properties": {"active": 0}, "classes": {"active": 0}, "computed-properties": {"active": 0}, "decorators": {"active": 0}, "destructuring": {"active": 0}, "do-expressions": {"active": 0}, "dotall-regex": {"active": 0}, "duplicate-keys": {"active": 0}, "exponentiation-operator": {"active": 0}, "export-default-from": {"active": 0}, "export-namespace-from": {"active": 0}, "external-helpers": {"active": 0}, "flow-strip-types": {"active": 0}, "for-of": {"active": 0}, "function-bind": {"active": 0}, "function-name": {"active": 0}, "function-sent": {"active": 0}, "inline-consecutive-adds": {"active": 0}, "inline-environment-variables": {"active": 0}, "instanceof": {"active": 0}, "jscript": {"active": 0}, "literals": {"active": 0}, "logical-assignment-operators": {"active": 0}, "member-expression-literals": {"active": 0}, "merge-sibling-variables": {"active": 0}, "minify-booleans": {"active": 0}, "minify-builtins": {"active": 0}, "minify-constant-folding": {"active": 0}, "minify-dead-code-elimination": {"active": 0}, "minify-flip-comparisons": {"active": 0}, "minify-guarded-expressions": {"active": 0}, "minify-infinity": {"active": 0}, "minify-mangle-names": {"active": 0}, "minify-numeric-literals": {"active": 0}, "minify-simplify": {"active": 0}, "minify-type-constructors": {"active": 0}, "modules-amd": {"active": 0}, "modules-commonjs": {"active": 0}, "modules-systemjs": {"active": 0}, "modules-umd": {"active": 0}, "named-capturing-groups-regex": {"active": 0}, "new-target": {"active": 0}, "node-env-inline": {"active": 0}, "nullish-coalescing-operator": {"active": 0}, "numeric-separator": {"active": 0}, "object-assign": {"active": 0}, "object-rest-spread": {"active": 0}, "object-set-prototype-of-to-assign": {"active": 0}, "object-super": {"active": 0}, "optional-catch-binding": {"active": 0}, "optional-chaining": {"active": 0}, "parameters": {"active": 0}, "partial-application": {"active": 0}, "pipeline-operator": {"active": 0}, "private-methods": {"active": 0}, "property-literals": {"active": 0}, "property-mutators": {"active": 0}, "proto-to-assign": {"active": 0}, "react-constant-elements": {"active": 0}, "react-display-name": {"active": 0}, "react-inline-elements": {"active": 0}, "react-jsx": {"active": 0}, "react-jsx-compat": {"active": 0}, "react-jsx-self": {"active": 0}, "react-jsx-source": {"active": 0}, "regenerator": {"active": 0}, "regexp-constructors": {"active": 0}, "remove-console": {"active": 0}, "remove-debugger": {"active": 0}, "remove-undefined": {"active": 0}, "reserved-words": {"active": 0}, "runtime": {"active": 0}, "shorthand-properties": {"active": 0}, "simplify-comparison-operators": {"active": 0}, "spread": {"active": 0}, "sticky-regex": {"active": 0}, "strict-mode": {"active": 0}, "template-literals": {"active": 0}, "throw-expressions": {"active": 0}, "typeof-symbol": {"active": 0}, "undefined-to-void": {"active": 0}, "unicode-property-regex": {"active": 0}, "unicode-regex": {"active": 0}}, "babelRetainLines": 0, "babelUseBuiltInsType": 0, "bowerAbbreviatedPath": "bower_components", "bowerForceLatestOnConflict": 1, "bowerTargetDependencyListType": 1, "bowerUseExactVersion": 0, "browserRefreshDelay": 0, "browserslistString": ">0.2%, last 2 versions, Firefox ESR, not dead", "buildEnvironment": 0, "buildFolderActive": 0, "buildFolderName": "build", "cleanBuild": 1, "coffeeLintFlags2": {"arrow_spacing": {"active": 0, "flagValue": -1}, "camel_case_classes": {"active": 1, "flagValue": -1}, "colon_assignment_spacing": {"active": 0, "flagValue": 1}, "cyclomatic_complexity": {"active": 0, "flagValue": 10}, "duplicate_key": {"active": 1, "flagValue": -1}, "empty_constructor_needs_parens": {"active": 0, "flagValue": -1}, "ensure_comprehensions": {"active": 1, "flagValue": -1}, "indentation": {"active": 1, "flagValue": 2}, "line_endings": {"active": 0, "flagValue": 0}, "max_line_length": {"active": 0, "flagValue": 150}, "missing_fat_arrows": {"active": 0, "flagValue": -1}, "newlines_after_classes": {"active": 0, "flagValue": 3}, "no_backticks": {"active": 1, "flagValue": -1}, "no_debugger": {"active": 1, "flagValue": -1}, "no_empty_functions": {"active": 0, "flagValue": -1}, "no_empty_param_list": {"active": 0, "flagValue": -1}, "no_implicit_braces": {"active": 1, "flagValue": -1}, "no_implicit_parens": {"active": 0, "flagValue": -1}, "no_interpolation_in_single_quotes": {"active": 0, "flagValue": -1}, "no_nested_string_interpolation": {"active": 1, "flagValue": -1}, "no_plusplus": {"active": 0, "flagValue": -1}, "no_private_function_fat_arrows": {"active": 1, "flagValue": -1}, "no_stand_alone_at": {"active": 1, "flagValue": -1}, "no_tabs": {"active": 1, "flagValue": -1}, "no_this": {"active": 0, "flagValue": -1}, "no_throwing_strings": {"active": 1, "flagValue": -1}, "no_trailing_semicolons": {"active": 1, "flagValue": -1}, "no_trailing_whitespace": {"active": 1, "flagValue": -1}, "no_unnecessary_double_quotes": {"active": 0, "flagValue": -1}, "no_unnecessary_fat_arrows": {"active": 1, "flagValue": -1}, "non_empty_constructor_needs_parens": {"active": 0, "flagValue": -1}, "prefer_english_operator": {"active": 0, "flagValue": -1}, "space_operators": {"active": 0, "flagValue": -1}, "spacing_after_comma": {"active": 1, "flagValue": -1}}, "cssoForceMediaMerge": 0, "cssoRestructure": 1, "environmentVariableEntries": ["NODE_ENV:::production"], "esLintConfigFileHandlingType": 0, "esLintECMAVersion": 7, "esLintEnvironmentsMask": 1, "esLintRules": {"accessor-pairs": {"active": 0, "optionString": "{'setWithoutGet': true, 'getWithoutSet': false}"}, "array-bracket-newline": {"active": 0, "optionString": "{'multiline': true, 'minItems': null}"}, "array-bracket-spacing": {"active": 0, "optionString": "'never', {'singleValue': false, 'objectsInArrays': false, 'arraysInArrays': false}"}, "array-callback-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "array-element-newline": {"active": 0, "optionString": "'always'"}, "arrow-body-style": {"active": 0, "optionString": "'as-needed', {'requireReturnForObjectLiteral': false}"}, "arrow-parens": {"active": 0, "optionString": "'always'"}, "arrow-spacing": {"active": 0, "optionString": "{'before': true, 'after': true}"}, "block-scoped-var": {"active": 0}, "block-spacing": {"active": 0, "optionString": "'always'"}, "brace-style": {"active": 0, "optionString": "'1tbs', {'allowSingleLine': true}"}, "callback-return": {"active": 0, "optionString": "['callback', 'cb', 'next']"}, "camelcase": {"active": 0, "optionString": "{'properties': 'always'}"}, "capitalized-comments": {"active": 0, "optionString": "'always', {'ignoreInlineComments': false, 'ignoreConsecutiveComments': false}"}, "class-methods-use-this": {"active": 0, "optionString": "{'exceptMethods': []}"}, "comma-dangle": {"active": 1, "optionString": "'never'"}, "comma-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "comma-style": {"active": 0, "optionString": "'last'"}, "complexity": {"active": 0, "optionString": "20"}, "computed-property-spacing": {"active": 0, "optionString": "'never'"}, "consistent-return": {"active": 0, "optionString": "{'treatUndefinedAsUnspecified': false}"}, "consistent-this": {"active": 0, "optionString": "'that'"}, "constructor-super": {"active": 1}, "curly": {"active": 0, "optionString": "'all'"}, "default-case": {"active": 0}, "default-case-last": {"active": 0}, "default-param-last": {"active": 0}, "dot-location": {"active": 0, "optionString": "'object'"}, "dot-notation": {"active": 0, "optionString": "{'allowKeywords': false}"}, "eol-last": {"active": 0, "optionString": "'always'"}, "eqeqeq": {"active": 0, "optionString": "'always', {'null': 'always'}"}, "for-direction": {"active": 0}, "func-call-spacing": {"active": 0, "optionString": "'never'"}, "func-name-matching": {"active": 0, "optionString": "'always', {'includeCommonJSModuleExports': false}"}, "func-names": {"active": 0, "optionString": "'always'"}, "func-style": {"active": 0, "optionString": "'expression'"}, "function-paren-newline": {"active": 0, "optionString": "'multiline'"}, "generator-star-spacing": {"active": 0, "optionString": "{'before': true, 'after': false}"}, "getter-return": {"active": 0, "optionString": "{'allowImplicit': false}"}, "global-require": {"active": 0}, "grouped-accessor-pairs": {"active": 0, "optionString": "'anyOrder'"}, "guard-for-in": {"active": 0}, "handle-callback-err": {"active": 0, "optionString": "'err'"}, "id-blacklist": {"active": 0, "optionString": "'data', 'err', 'e', 'cb', 'callback'"}, "id-length": {"active": 0, "optionString": "{'min': 2, 'max': 1000, 'properties': 'always', 'exceptions': ['x', 'i', 'y']}"}, "id-match": {"active": 0, "optionString": "'^[a-z]+([A-Z][a-z]+)*$', {'properties': false, 'onlyDeclarations': true}"}, "implicit-arrow-linebreak": {"active": 0, "optionString": "'beside'"}, "indent": {"active": 0, "optionString": "4, {'SwitchCase': 0, 'VariableDeclarator': 1, 'outerIIFEBody': 1 }"}, "init-declarations": {"active": 0, "optionString": "'always',  {'ignoreForLoopInit': true}"}, "jsx-quotes": {"active": 0, "optionString": "'prefer-double'"}, "key-spacing": {"active": 0, "optionString": "{'singleLine': {'beforeColon': false, 'afterColon': true, 'mode':'strict'}, 'multiLine': {'beforeColon': false, 'afterColon': true, 'align': 'value', 'mode':'minimum'}}"}, "keyword-spacing": {"active": 0, "optionString": "{'before': true, 'after': true, 'overrides': {}}"}, "line-comment-position": {"active": 0, "optionString": "{'position': 'above'}"}, "linebreak-style": {"active": 0, "optionString": "'unix'"}, "lines-around-comment": {"active": 0, "optionString": "{'beforeBlockComment': true}"}, "lines-between-class-members": {"active": 0, "optionString": "'always', {exceptAfterSingleLine: false}"}, "max-classes-per-file": {"active": 0, "optionString": "{'ignoreExpressions': false, 'max': 1}"}, "max-depth": {"active": 0, "optionString": "{'max': 4}"}, "max-len": {"active": 0, "optionString": "{'code': 80, 'comments': 80, 'tabWidth': 4, 'ignoreUrls': true, 'ignoreStrings': true, 'ignoreTemplateLiterals': true, 'ignoreRegExpLiterals': true}"}, "max-lines": {"active": 0, "optionString": "{'max': 300, 'skipBlankLines': true, 'skipComments': true}"}, "max-lines-per-function": {"active": 0, "optionString": "{'max': 50, 'skipBlankLines': true, 'skipComments': true, 'IIFEs': false}"}, "max-nested-callbacks": {"active": 0, "optionString": "{'max': 10}"}, "max-params": {"active": 0, "optionString": "{'max': 4}"}, "max-statements": {"active": 0, "optionString": "{'max': 10}, {'ignoreTopLevelFunctions': true}"}, "max-statements-per-line": {"active": 0, "optionString": "{'max': 1}"}, "multiline-comment-style": {"active": 0, "optionString": "'starred-block'"}, "multiline-ternary": {"active": 0, "optionString": "'always'"}, "new-cap": {"active": 0, "optionString": "{'newIsCap': true, 'capIsNew': true, 'newIsCapExceptions': [], 'capIsNewExceptions': ['Array', 'Boolean', 'Date', 'Error', 'Function', 'Number', 'Object', 'RegExp', 'String', 'Symbol'], 'properties': true}"}, "new-parens": {"active": 0, "optionString": ""}, "newline-per-chained-call": {"active": 0, "optionString": "{'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>': 2}"}, "no-alert": {"active": 0}, "no-array-constructor": {"active": 0}, "no-async-promise-executor": {"active": 0}, "no-await-in-loop": {"active": 0}, "no-bitwise": {"active": 0, "optionString": "{'allow': ['~'], 'int32Hint': true}"}, "no-buffer-constructor": {"active": 0}, "no-caller": {"active": 0}, "no-case-declarations": {"active": 1}, "no-class-assign": {"active": 1}, "no-compare-neg-zero": {"active": 0}, "no-cond-assign": {"active": 1, "optionString": "'except-parens'"}, "no-confusing-arrow": {"active": 0, "optionString": "{'allowParens': false}"}, "no-console": {"active": 1, "optionString": "{'allow': ['warn', 'error']}"}, "no-const-assign": {"active": 1}, "no-constant-condition": {"active": 1, "optionString": "{'checkLoops': true}"}, "no-constructor-return": {"active": 0}, "no-continue": {"active": 0}, "no-control-regex": {"active": 1}, "no-debugger": {"active": 1}, "no-delete-var": {"active": 1}, "no-div-regex": {"active": 0}, "no-dupe-args": {"active": 1}, "no-dupe-class-members": {"active": 1}, "no-dupe-else-if": {"active": 1}, "no-dupe-keys": {"active": 1}, "no-duplicate-imports": {"active": 0, "optionString": "{'includeExports': false}"}, "no-else-return": {"active": 0}, "no-empty": {"active": 1, "optionString": "{'allowEmptyCatch': false}"}, "no-empty-character-class": {"active": 1}, "no-empty-function": {"active": 0, "optionString": "{'allow': []}"}, "no-empty-pattern": {"active": 1}, "no-eq-null": {"active": 0}, "no-eval": {"active": 0, "optionString": "{'allowIndirect': false}"}, "no-ex-assign": {"active": 1}, "no-extend-native": {"active": 0, "optionString": "{'exceptions': []}"}, "no-extra-bind": {"active": 0}, "no-extra-boolean-cast": {"active": 1}, "no-extra-labels": {"active": 0}, "no-extra-parens": {"active": 0, "optionString": "'all', {'conditionalAssign': false, 'returnAssign': false, 'nestedBinaryExpressions': false, 'ignoreJSX': 'none', 'enforceForArrowConditionals': false}"}, "no-extra-semi": {"active": 1}, "no-fallthrough": {"active": 1}, "no-floating-decimal": {"active": 0}, "no-func-assign": {"active": 1}, "no-global-assign": {"active": 1, "optionString": "{'exceptions': []}"}, "no-implicit-coercion": {"active": 0, "optionString": "{'boolean': true, 'number': true, 'string': true, 'allow': []}"}, "no-implicit-globals": {"active": 0}, "no-implied-eval": {"active": 0}, "no-import-assign": {"active": 1}, "no-inline-comments": {"active": 0}, "no-inner-declarations": {"active": 1, "optionString": "'functions'"}, "no-invalid-regexp": {"active": 1, "optionString": "{'allowConstructorFlags': ['u', 'y']}"}, "no-invalid-this": {"active": 0, "optionString": ""}, "no-irregular-whitespace": {"active": 1, "optionString": "{'skipStrings': true, 'skipComments': false, 'skipRegExps': true, 'skipTemplates': true}"}, "no-iterator": {"active": 0}, "no-label-var": {"active": 0}, "no-labels": {"active": 0, "optionString": "{'allowLoop': false, 'allowSwitch': false}"}, "no-lone-blocks": {"active": 0}, "no-lonely-if": {"active": 0}, "no-loop-func": {"active": 0}, "no-loss-of-precision": {"active": 1}, "no-magic-numbers": {"active": 0, "optionString": "{'ignore': [], 'ignoreArrayIndexes': true, 'enforceConst': false, 'detectObjects': false}"}, "no-misleading-character-class": {"active": 0}, "no-mixed-operators": {"active": 0, "optionString": "{'groups': [['+', '-', '*', '/', '%', '**'], ['&', '|', '^', '~', '<<', '>>', '>>>'], ['==', '!=', '===', '!==', '>', '>=', '<', '<='], ['&&', '||'], ['in', 'instanceof']], 'allowSamePrecedence': true}"}, "no-mixed-requires": {"active": 0, "optionString": "{'grouping': false, 'allowCall': false }"}, "no-mixed-spaces-and-tabs": {"active": 0, "optionString": ""}, "no-multi-assign": {"active": 0, "optionString": ""}, "no-multi-spaces": {"active": 0, "optionString": "{'exceptions': {'Property': true, 'BinaryExpression': false, 'VariableDeclarator': false, 'ImportDeclaration': false}}"}, "no-multi-str": {"active": 0}, "no-multiple-empty-lines": {"active": 0, "optionString": "{'max': 2, 'maxBOF': 2, 'maxEOF': 2}"}, "no-negated-condition": {"active": 0}, "no-nested-ternary": {"active": 0}, "no-new": {"active": 0}, "no-new-func": {"active": 0}, "no-new-object": {"active": 0}, "no-new-require": {"active": 0}, "no-new-symbol": {"active": 1}, "no-new-wrappers": {"active": 0}, "no-nonoctal-decimal-escape": {"active": 1}, "no-obj-calls": {"active": 1}, "no-octal": {"active": 1}, "no-octal-escape": {"active": 0}, "no-param-reassign": {"active": 0, "optionString": "{'props': false}"}, "no-path-concat": {"active": 0}, "no-plusplus": {"active": 0, "optionString": "{'allowForLoopAfterthoughts': false}"}, "no-process-env": {"active": 0}, "no-process-exit": {"active": 0}, "no-promise-executor-return": {"active": 0}, "no-proto": {"active": 0}, "no-prototype-builtins": {"active": 0}, "no-redeclare": {"active": 1, "optionString": "{'builtinGlobals': false}"}, "no-regex-spaces": {"active": 1}, "no-restricted-exports": {"active": 0, "optionString": "{'restrictedNamedExports': []}"}, "no-restricted-globals": {"active": 0, "optionString": "'event', 'fdescribe'"}, "no-restricted-imports": {"active": 0}, "no-restricted-modules": {"active": 0, "optionString": ""}, "no-restricted-properties": {"active": 0, "optionString": "[{'object': 'disallowedObjectName', 'property': 'disallowedPropertyName'}, {'object': 'disallowedObjectName', 'property': 'anotherDisallowedPropertyName', 'message': 'Please use allowedObjectName.allowedPropertyName.'}]"}, "no-restricted-syntax": {"active": 0, "optionString": "'FunctionExpression', 'WithStatement'"}, "no-return-assign": {"active": 0, "optionString": "'except-parens'"}, "no-return-await": {"active": 0}, "no-script-url": {"active": 0}, "no-self-assign": {"active": 1, "optionString": "{'props': false}"}, "no-self-compare": {"active": 0}, "no-sequences": {"active": 0, "optionString": ""}, "no-setter-return": {"active": 1}, "no-shadow": {"active": 0, "optionString": "{'builtinGlobals': false, 'hoist': 'functions', 'allow': []}"}, "no-shadow-restricted-names": {"active": 0}, "no-sparse-arrays": {"active": 1}, "no-sync": {"active": 0, "optionString": "{'allowAtRootLevel': false}"}, "no-tabs": {"active": 0, "optionString": ""}, "no-template-curly-in-string": {"active": 0}, "no-ternary": {"active": 0}, "no-this-before-super": {"active": 1}, "no-throw-literal": {"active": 0}, "no-trailing-spaces": {"active": 0, "optionString": "{'skipBlankLines': false, 'ignoreComments': false}"}, "no-undef": {"active": 1, "optionString": "{'typeof': false}"}, "no-undef-init": {"active": 0}, "no-undefined": {"active": 0}, "no-underscore-dangle": {"active": 0, "optionString": "{'allow': [], 'allowAfterThis': false, 'allowAfterSuper': false, 'enforceInMethodNames': false}"}, "no-unexpected-multiline": {"active": 1}, "no-unmodified-loop-condition": {"active": 0}, "no-unneeded-ternary": {"active": 0, "optionString": "{'defaultAssignment': true}"}, "no-unreachable": {"active": 1}, "no-unreachable-loop": {"active": 0, "optionString": "{'ignore': []}"}, "no-unsafe-finally": {"active": 1}, "no-unsafe-negation": {"active": 1, "optionString": ""}, "no-unsafe-optional-chaining": {"active": 1, "optionString": "{'disallowArithmeticOperators': false}"}, "no-unused-expressions": {"active": 0, "optionString": "{'allowShortCircuit': false, 'allowTernary': false, 'allowTaggedTemplates': false}"}, "no-unused-labels": {"active": 1}, "no-unused-private-class-members": {"active": 0}, "no-unused-vars": {"active": 1, "optionString": "{'vars': 'all', 'args': 'after-used', 'caughtErrors': 'none', 'ignoreRestSiblings': false}"}, "no-use-before-define": {"active": 0, "optionString": "{'functions': true, 'classes': true, 'variables': true}"}, "no-useless-backreference": {"active": 1}, "no-useless-call": {"active": 0}, "no-useless-catch": {"active": 0}, "no-useless-computed-key": {"active": 0, "optionString": ""}, "no-useless-concat": {"active": 0}, "no-useless-constructor": {"active": 0}, "no-useless-escape": {"active": 0}, "no-useless-rename": {"active": 0, "optionString": "{'ignoreDestructuring': false, 'ignoreImport': false, 'ignoreExport': false}"}, "no-useless-return": {"active": 0}, "no-var": {"active": 0}, "no-void": {"active": 0, "optionString": ""}, "no-warning-comments": {"active": 0, "optionString": "{'terms': ['todo', 'fixme', 'xxx'], 'location': 'start'}"}, "no-whitespace-before-property": {"active": 0}, "no-with": {"active": 0}, "nonblock-statement-body-position": {"active": 0, "optionString": "'beside'"}, "object-curly-newline": {"active": 0, "optionString": "{'ObjectExpression': {'multiline': true}, 'ObjectPattern': {'multiline': true}}"}, "object-curly-spacing": {"active": 0, "optionString": "'never'"}, "object-property-newline": {"active": 0, "optionString": "{'allowAllPropertiesOnSameLine': true}"}, "object-shorthand": {"active": 0, "optionString": "'always', {'avoidQuotes': false, 'ignoreConstructors': false}"}, "one-var": {"active": 0, "optionString": "'always'"}, "one-var-declaration-per-line": {"active": 0, "optionString": "'always'"}, "operator-assignment": {"active": 0, "optionString": "'always'"}, "operator-linebreak": {"active": 0, "optionString": "'after', {'overrides': {'?': 'after', '+=': 'none'}}"}, "padded-blocks": {"active": 0, "optionString": "{'blocks': 'always', 'switches': 'always', 'classes': 'always'}"}, "padding-line-between-statements": {"active": 0, "optionString": "{blankLine: 'always', prev:'*', next:'return'}"}, "prefer-arrow-callback": {"active": 0}, "prefer-const": {"active": 0, "optionString": "{'destructuring': 'any', 'ignoreReadBeforeAssign': false}"}, "prefer-destructuring": {"active": 0, "optionString": "{'array': true, 'object': true}, {'enforceForRenamedProperties': false}"}, "prefer-exponentiation-operator": {"active": 0}, "prefer-named-capture-group": {"active": 0}, "prefer-numeric-literals": {"active": 0}, "prefer-object-spread": {"active": 0}, "prefer-promise-reject-errors": {"active": 0, "optionString": "{'allowEmptyReject': false}"}, "prefer-regex-literals": {"active": 0}, "prefer-rest-params": {"active": 0}, "prefer-spread": {"active": 0}, "prefer-template": {"active": 0}, "quote-props": {"active": 0, "optionString": "'always'"}, "quotes": {"active": 0, "optionString": "'double', {'avoidEscape': true, 'allowTemplateLiterals': true}"}, "radix": {"active": 0, "optionString": "'always'"}, "require-atomic-updates": {"active": 0}, "require-await": {"active": 0}, "require-jsdoc": {"active": 0, "optionString": "{'require': {'FunctionDeclaration': true, 'MethodDefinition': false, 'ClassDeclaration': false, 'ArrowFunctionExpression': false}}"}, "require-unicode-regexp": {"active": 0}, "require-yield": {"active": 1}, "rest-spread-spacing": {"active": 0, "optionString": "'never'"}, "semi": {"active": 0, "optionString": "'always', {'omitLastInOneLineBlock': false}"}, "semi-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "semi-style": {"active": 0, "optionString": "'last'"}, "sort-imports": {"active": 0, "optionString": "{'ignoreCase': false, 'ignoreMemberSort': true, 'memberSyntaxSortOrder': ['none', 'all', 'multiple', 'single']}"}, "sort-keys": {"active": 0, "optionString": "'asc', {'caseSensitive': true, 'natural': false}"}, "sort-vars": {"active": 0, "optionString": "{'ignoreCase': false}"}, "space-before-blocks": {"active": 0, "optionString": "{'functions': 'always', 'keywords': 'always', 'classes': 'always'}"}, "space-before-function-paren": {"active": 0, "optionString": "{'anonymous': 'always', 'named': 'never'}"}, "space-in-parens": {"active": 0, "optionString": "'never', {'exceptions': []}"}, "space-infix-ops": {"active": 0, "optionString": "{'int32Hint': false}"}, "space-unary-ops": {"active": 0, "optionString": "{'words': true, 'nonwords': false, 'overrides': {}}"}, "spaced-comment": {"active": 0, "optionString": "'always', {'line': {'markers': ['/'], 'exceptions': ['-', '+']}, 'block': {'markers': ['!'], 'exceptions': ['*'], 'balanced': false}}"}, "strict": {"active": 0, "optionString": "'safe'"}, "switch-colon-spacing": {"active": 0, "optionString": "{'after': true, 'before': false}"}, "symbol-description": {"active": 0}, "template-curly-spacing": {"active": 0, "optionString": "'never'"}, "template-tag-spacing": {"active": 0, "optionString": "'never'"}, "unicode-bom": {"active": 0, "optionString": "'never'"}, "use-isnan": {"active": 1, "optionString": ""}, "valid-jsdoc": {"active": 0, "optionString": "{'prefer': {'return': 'returns'}, 'requireReturn': true, 'requireReturnDescription': true, 'requireReturnType': true, 'requireParamDescription': true}"}, "valid-typeof": {"active": 1, "optionString": "{'requireStringLiterals': true}"}, "vars-on-top": {"active": 0}, "wrap-iife": {"active": 0, "optionString": "'outside'"}, "wrap-regex": {"active": 0}, "yield-star-spacing": {"active": 0, "optionString": "{'before': false, 'after': true}"}, "yoda": {"active": 0, "optionString": "'never', {'except<PERSON><PERSON><PERSON>': false, 'onlyEquality': false}"}}, "esLintSourceType": 0, "externalServerAddress": "https://superangel.site.dev/", "gitIgnoreBuildFolder": 1, "hideConfigFile": 0, "jsCheckerReservedNamesString": "", "languageDefaultsCOFFEE": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "minifierStyle": 1, "outputStyle": 0, "sourceMapStyle": 0, "syntaxCheckerStyle": 1, "transpilerStyle": 1}, "languageDefaultsCSS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.css", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "combineImports": 0, "cssoStyle": 0, "purgeCSSStyle": 0, "shouldRunAutoprefixer": 1, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsGIF": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.gif", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsHAML": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "escapeHTMLCharacters": 0, "htmlMinifierStyle": 0, "noEscapeInAttributes": 0, "outputFormat": 2, "shouldRunCacheBuster": 0, "useCDATA": 0, "useDoubleQuotes": 0, "useUnixNewlines": 0}, "languageDefaultsJPG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.jpg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "outputFormat": 0, "quality": 100, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsJS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*-min.js", "autoOutputPathRelativePath": "/min", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "bundleFormat": 0, "minifierStyle": 1, "sourceMapStyle": 0, "syntaxCheckerStyle": 3, "transpilerStyle": 0}, "languageDefaultsJSON": {"autoOutputAction": 1, "autoOutputPathFilenamePattern": "*-min.json", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "orderOutput": 0, "outputStyle": 1}, "languageDefaultsKIT": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "kit", "autoOutputPathReplace2": "html", "autoOutputPathStyle": 0, "htmlMinifierStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsLESS": {"allowInsecureImports": 0, "autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "less", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "cssoStyle": 0, "enableJavascript": 0, "mathStyle": 0, "outputStyle": 0, "purgeCSSStyle": 0, "rewriteURLStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 1, "strictImports": 0, "strictUnits": 0}, "languageDefaultsMARKDOWN": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "criticStyle": 0, "enableFootnotes": 1, "enableLabels": 1, "enableSmartQuotes": 1, "htmlMinifierStyle": 0, "maskEmailAddresses": 1, "outputFormat": 0, "outputStyle": 0, "parseMetadata": 1, "processHTML": 0, "randomFootnoteNumbers": 0, "shouldRunCacheBuster": 0, "useCompatibilityMode": 0}, "languageDefaultsOTHER": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.*", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "htmlMinifierStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsPNG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.png", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "optimizerType": 1, "quality": 100, "webpOptimizationPresetUUID": "lpckwebp-none", "webpRGBQuality": 75}, "languageDefaultsPUG": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileDebug": 1, "htmlMinifierStyle": 0, "outputStyle": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSASS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "sass", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "compilerType": 1, "cssoStyle": 0, "decimalPrecision": 10, "emitCharset": 1, "outputStyle": 0, "purgeCSSStyle": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsSLIM": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.html", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "compileOnly": 0, "htmlMinifierStyle": 0, "logicless": 0, "outputFormat": 0, "outputStyle": 1, "railsCompatible": 0, "shouldRunCacheBuster": 0}, "languageDefaultsSTYLUS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.css", "autoOutputPathRelativePath": "../css", "autoOutputPathReplace1": "stylus", "autoOutputPathReplace2": "css", "autoOutputPathStyle": 0, "cssoStyle": 0, "debugStyle": 0, "importCSS": 0, "outputStyle": 0, "purgeCSSStyle": 0, "resolveRelativeURLS": 0, "shouldRunAutoprefixer": 0, "shouldRunBless": 0, "sourceMapStyle": 0}, "languageDefaultsSVG": {"autoOutputAction": 2, "autoOutputPathFilenamePattern": "*.svg", "autoOutputPathRelativePath": "", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "pluginMask": 3758088159}, "languageDefaultsTS": {"autoOutputAction": 0, "autoOutputPathFilenamePattern": "*.js", "autoOutputPathRelativePath": "/js", "autoOutputPathReplace1": "", "autoOutputPathReplace2": "", "autoOutputPathStyle": 0, "createDeclarationFile": 0, "jsxMode": 0, "minifierStyle": 0, "moduleResolutionType": 0, "moduleType": 2, "removeComments": 0, "sourceMapStyle": 0, "targetECMAVersion": 0}, "languageDefaultsUserDefined": [], "npmAbbreviatedPath": "", "npmCreatePackageLock": 1, "npmInstallOptionalDependencies": 0, "npmSaveExactVersion": 0, "npmTargetDependencyListType": 1, "overrideExternalServerCSS": 0, "previewPathAddition": "", "purgeCSS": {"blocklistEntries": [], "contentEntries": ["**/*.html", "**/*.htm", "**/*.shtml", "**/*.xhtml", "**/*.php", "**/*.js", "**/*.ts", "**/*.coffee", "**/*.erb", "**/*.pug", "**/*.jade", "**/*.slim", "**/*.haml", "**/*.md", "**/*.kit"], "removeFontFace": 0, "removeKeyframes": 0, "removeVariables": 0, "safelistEntries": [], "skippedEntries": ["node_modules/**"]}, "rollupContext": "", "rollupExternalEntries": [], "rollupReplacementEntries": ["process.env.NODE_ENV:::$NODE_ENV", "ENVIRONMENT:::$NODE_ENV"], "rollupTreeshakingEnabled": 1, "skippedFoldersString": "log, _logs, logs, _cache, cache, .idea, /storage/framework/sessions, node_modules", "sourceFolderName": "source", "susyVersion": 3, "tsAllowSyntheticDefaultImports": 0, "tsAllowUnreachableCode": 0, "tsAllowUnusedLabels": 0, "tsAlwaysStrict": 0, "tsDownlevelIteration": 0, "tsEmitBOM": 0, "tsEmitDecoratorMetadata": 0, "tsESModuleInterop": 0, "tsExactOptionalPropertyTypes": 0, "tsForceConsistentCasingInFileNames": 0, "tsImportHelpers": 0, "tsIsolatedModules": 0, "tsJSXFactory": "React.createElement", "tsKeyofStringsOnly": 0, "tsNoEmitHelpers": 0, "tsNoFallthroughCasesInSwitch": 0, "tsNoImplicitAny": 0, "tsNoImplicitOverride": 0, "tsNoImplicitReturns": 0, "tsNoImplicitThis": 0, "tsNoImplicitUseStrict": 0, "tsNoLib": 0, "tsNoPropertyAccessFromIndexSignature": 0, "tsNoResolve": 0, "tsNoStrictGenericChecks": 0, "tsNoUncheckedIndexAccess": 0, "tsNoUnusedLocals": 0, "tsNoUnusedParameters": 0, "tsPreserveConstEnums": 0, "tsPreserveSymlinks": 0, "tsResolveJsonModule": 0, "tsSkipLibCheck": 0, "tsStrictFunctionTypes": 0, "tsStrictNullChecks": 0, "tsStrictPropertyInitialization": 0, "tsStripInternal": 0, "tsSuppressExcessPropertyErrors": 0, "tsSuppressImplicitAnyIndexErrors": 0, "tsUseUnknownInCatchVariables": 0, "uglifyDefinesString": "", "uglifyFlags2": {"arguments": {"active": 1, "flagValue": -1}, "arrows": {"active": 1, "flagValue": -1}, "ascii_only": {"active": 0, "flagValue": -1}, "booleans": {"active": 1, "flagValue": -1}, "booleans_as_integers": {"active": 0, "flagValue": -1}, "braces": {"active": 0, "flagValue": -1}, "collapse_vars": {"active": 1, "flagValue": -1}, "comments": {"active": 0, "flagValue": -1}, "comparisons": {"active": 1, "flagValue": -1}, "computed_props": {"active": 1, "flagValue": -1}, "conditionals": {"active": 1, "flagValue": -1}, "dead_code": {"active": 0, "flagValue": -1}, "directives": {"active": 1, "flagValue": -1}, "drop_console": {"active": 0, "flagValue": -1}, "drop_debugger": {"active": 1, "flagValue": -1}, "ecma": {"active": 1, "flagValue": 5}, "eval": {"active": 0, "flagValue": -1}, "evaluate": {"active": 1, "flagValue": -1}, "expression": {"active": 0, "flagValue": -1}, "hoist_funs": {"active": 1, "flagValue": -1}, "hoist_props": {"active": 1, "flagValue": -1}, "hoist_vars": {"active": 0, "flagValue": -1}, "ie8": {"active": 0, "flagValue": -1}, "if_return": {"active": 1, "flagValue": -1}, "indent_level": {"active": 0, "flagValue": 4}, "indent_start": {"active": 0, "flagValue": 0}, "inline": {"active": 1, "flagValue": 3}, "inline_script": {"active": 1, "flagValue": -1}, "join_vars": {"active": 1, "flagValue": -1}, "keep_classnames": {"active": 0, "flagValue": -1}, "keep_fargs": {"active": 0, "flagValue": -1}, "keep_fnames": {"active": 0, "flagValue": -1}, "keep_infinity": {"active": 0, "flagValue": -1}, "keep_quoted_props": {"active": 0, "flagValue": -1}, "loops": {"active": 1, "flagValue": -1}, "max_line_len": {"active": 1, "flagValue": 32000}, "module": {"active": 0, "flagValue": -1}, "negate_iife": {"active": 1, "flagValue": -1}, "passes": {"active": 1, "flagValue": 1}, "properties": {"active": 1, "flagValue": -1}, "pure_getters": {"active": 0, "flagValue": -1}, "quote_keys": {"active": 0, "flagValue": -1}, "quote_style": {"active": 1, "flagValue": 0}, "reduce_funcs": {"active": 1, "flagValue": -1}, "reduce_vars": {"active": 1, "flagValue": -1}, "safari10": {"active": 0, "flagValue": -1}, "semicolons": {"active": 1, "flagValue": -1}, "sequences": {"active": 1, "flagValue": -1}, "shebang": {"active": 1, "flagValue": -1}, "side_effects": {"active": 1, "flagValue": -1}, "switches": {"active": 1, "flagValue": -1}, "toplevel": {"active": 0, "flagValue": -1}, "typeofs": {"active": 1, "flagValue": -1}, "unsafe": {"active": 0, "flagValue": -1}, "unsafe_arrows": {"active": 0, "flagValue": -1}, "unsafe_comps": {"active": 0, "flagValue": -1}, "unsafe_Function": {"active": 0, "flagValue": -1}, "unsafe_math": {"active": 0, "flagValue": -1}, "unsafe_methods": {"active": 0, "flagValue": -1}, "unsafe_proto": {"active": 0, "flagValue": -1}, "unsafe_regexp": {"active": 0, "flagValue": -1}, "unsafe_undefined": {"active": 0, "flagValue": -1}, "unused": {"active": 0, "flagValue": -1}, "warnings": {"active": 0, "flagValue": -1}, "webkit": {"active": 0, "flagValue": -1}, "wrap_iife": {"active": 0, "flagValue": -1}}, "uglifyMangleNames": 1, "uglifyReservedNamesString": "$", "webpPresets": {}, "websiteRelativeRoot": ""}, "settingsFileVersion": "3"}